USE [Tiba_Pos]
GO
/****** Object:  Table [dbo].[Units]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Units](
	[UnitID] [int] NOT NULL,
	[UnitName] [nvarchar](50) NULL,
	[UnitShortName] [nvarchar](50) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Waiters]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Waiters](
	[WaiterID] [int] IDENTITY(1,1) NOT NULL,
	[WaiterCode] [nvarchar](50) NULL,
	[WaiterName] [nvarchar](150) NULL,
	[PW] [nvarchar](150) NULL,
	[AllowLogin] [bit] NULL,
	[Active] [bit] NULL,
	[CCID] [int] NULL,
	[TblID] [int] NULL,
	[WTeamID] [int] NULL,
	[MagKey] [varchar](80) NULL,
	[RoleID] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[CCGroups]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CCGroups](
	[CCGID] [int] IDENTITY(1,1) NOT NULL,
	[CCGCode] [nvarchar](20) NULL,
	[CCGName] [nvarchar](100) NULL,
	[TGID] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Check_Details]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Check_Details](
	[ChDID] [bigint] IDENTITY(1,1) NOT NULL,
	[ChkPrtID] [int] NULL,
	[ArtID] [int] NULL,
	[Qty] [decimal](19, 5) NULL,
	[UnitPrice] [decimal](19, 5) NULL,
	[Price] [decimal](19, 5) NULL,
	[RQty] [decimal](19, 5) NULL,
	[RPrice] [decimal](19, 5) NULL,
	[Voided] [bit] NULL,
	[VoidDate] [datetime] NULL,
	[WaiterID_Void] [int] NULL,
	[PQty] [decimal](19, 5) NULL,
	[PRQty] [decimal](19, 5) NULL,
	[Printed] [bit] NULL,
	[NetAmount] [decimal](19, 5) NULL,
	[DeducAmount] [decimal](19, 5) NULL,
	[GrossAmount] [decimal](19, 5) NULL,
	[Discount] [decimal](19, 5) NULL,
	[DueAmount] [decimal](19, 5) NULL,
	[DID] [int] NULL,
	[OTID] [int] NULL,
	[TGID] [int] NULL,
	[FreePrice] [bit] NULL,
	[FreeTxt] [bit] NULL,
	[txt] [nvarchar](80) NULL,
	[MemoID] [int] NULL,
	[OrderDate] [datetime] NULL,
	[WaiterID_Order] [int] NULL,
	[DiscPct] [decimal](19, 2) NULL,
	[DiscFixed] [bit] NULL,
	[BaseUnitPrice] [decimal](19, 5) NULL,
	[ArtOrderNotes] [nvarchar](100) NULL,
	[IsIFCDone] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ChkParts]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ChkParts](
	[ChkPrtID] [int] IDENTITY(1,1) NOT NULL,
	[TblID] [int] NULL,
	[DSerial] [int] NULL,
	[CheckSerial] [int] NULL,
	[GlobSerial] [int] NULL,
	[PrtNo] [int] NULL,
	[Subtotal] [decimal](19, 5) NULL,
	[NetAmount] [decimal](19, 5) NULL,
	[DeducAmount] [decimal](19, 5) NULL,
	[GrossAmount] [decimal](19, 5) NULL,
	[Discount] [decimal](19, 5) NULL,
	[DueAmount] [decimal](19, 5) NULL,
	[DID] [int] NULL,
	[WaiterID_Open] [int] NULL,
	[OpenDate] [datetime] NULL,
	[Closed] [bit] NULL,
	[WaiterID_Close] [int] NULL,
	[ClosingDate] [datetime] NULL,
	[Voided] [bit] NULL,
	[VoidDate] [datetime] NULL,
	[WaiterID_Void] [int] NULL,
	[Printed] [bit] NULL,
	[GuestCount] [int] NULL,
	[MinCharge] [decimal](19, 5) NULL,
	[WaiterID_Owner] [int] NULL,
	[ClientID] [int] NULL,
	[SysDate] [datetime] NULL,
	[ShiftID] [int] NULL,
	[PCount] [int] NULL,
	[ChkNotes] [nvarchar](100) NULL,
	[MachineNU] [int] NULL,
	[IsEmployee] [bit] NULL,
	[IsMember] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[CostCenters]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CostCenters](
	[CCID] [int] IDENTITY(1,1) NOT NULL,
	[CCCode] [nvarchar](20) NULL,
	[CCName] [nvarchar](100) NULL,
	[CCGID] [int] NULL,
	[LstSerial] [int] NULL,
	[TGID] [int] NULL,
	[PLGID] [int] NULL,
	[MinCharge] [decimal](19, 5) NULL,
	[MCPerGuest] [bit] NULL,
	[OTID] [int] NULL,
	[MCardID] [int] NULL,
	[MinAmount] [decimal](19, 5) NULL,
	[RegnID] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Floors]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Floors](
	[FloorID] [int] IDENTITY(1,1) NOT NULL,
	[FloorName] [nvarchar](80) NULL,
	[Img] [nvarchar](200) NULL,
	[CCID] [int] NULL,
	[Fx] [int] NULL,
	[Fy] [int] NULL,
	[Fw] [int] NULL,
	[Fh] [int] NULL,
	[AskGuestCount] [bit] NULL,
	[IsNotActive] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Families]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Families](
	[FID] [int] IDENTITY(1,1) NOT NULL,
	[FCode] [nvarchar](20) NULL,
	[FName] [nvarchar](100) NULL,
	[FGID] [int] NULL,
	[TGID] [int] NULL,
	[Img] [nvarchar](255) NULL,
	[Priority] [int] NULL,
	[KDS] [bit] NULL,
	[InActive] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[FamilyGroups]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[FamilyGroups](
	[FGID] [int] IDENTITY(1,1) NOT NULL,
	[FGCode] [nvarchar](20) NULL,
	[FGName] [nvarchar](100) NULL,
	[TGID] [int] NULL,
	[FGAccNo] [nvarchar](50) NULL,
	[KDS] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[OrderTypes]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[OrderTypes](
	[OTID] [int] IDENTITY(1,1) NOT NULL,
	[OTCode] [nvarchar](20) NULL,
	[OTName] [nvarchar](80) NULL,
	[TaxGID] [int] NULL,
	[NotInSales] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Articles]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Articles](
	[ArtID] [int] IDENTITY(1,1) NOT NULL,
	[ArtCode] [nvarchar](20) NULL,
	[ArtName] [nvarchar](150) NULL,
	[Price] [decimal](19, 5) NULL,
	[ArtDesc] [nvarchar](200) NULL,
	[FID] [int] NULL,
	[Priority] [int] NULL,
	[TGID] [int] NULL,
	[BtnTxt] [nvarchar](100) NULL,
	[DisplayTxt] [nvarchar](100) NULL,
	[PrinterTxt] [nvarchar](100) NULL,
	[Inactive] [bit] NULL,
	[DID] [int] NULL,
	[FreePrice] [bit] NULL,
	[FreeTxt] [bit] NULL,
	[StkItm] [bit] NULL,
	[Stock] [decimal](19, 5) NULL,
	[Barcode] [nvarchar](50) NULL,
	[Img] [nvarchar](255) NULL,
	[UnitID] [int] NULL,
	[KDS] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Shifts]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Shifts](
	[ShiftID] [int] IDENTITY(1,1) NOT NULL,
	[ShiftCode] [nvarchar](20) NULL,
	[ShiftName] [nvarchar](50) NULL,
	[DateStart] [time](7) NULL,
	[DateEnd] [time](7) NULL,
	[DateStart_D] [datetime] NULL,
	[DateEnd_D] [datetime] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Tables]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Tables](
	[TblID] [int] IDENTITY(1,1) NOT NULL,
	[TblCode] [int] NULL,
	[TblName] [nvarchar](100) NULL,
	[TGID] [int] NULL,
	[CCID] [int] NULL,
	[PLGID] [int] NULL,
	[MinCharge] [decimal](19, 5) NULL,
	[MCPerGuest] [bit] NULL,
	[FloorID] [int] NULL,
	[TblStyleID] [int] NULL,
	[Fx] [int] NULL,
	[Fy] [int] NULL,
	[Fw] [int] NULL,
	[Fh] [int] NULL,
	[OTID] [int] NULL,
	[MCardID] [int] NULL,
	[MinAmount] [decimal](19, 5) NULL,
	[ChangeTableDisabled] [bit] NULL,
	[IsNotActive] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  View [dbo].[vOrders]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE VIEW [dbo].[vOrders]
AS
SELECT        dbo.CCGroups.CCGID, dbo.CCGroups.CCGCode, dbo.CCGroups.CCGName, dbo.CostCenters.CCID, dbo.CostCenters.CCCode, dbo.CostCenters.CCName, dbo.ChkParts.ChkPrtID, dbo.ChkParts.TblID, dbo.Tables.TblCode, 
                         dbo.Tables.TblName, dbo.ChkParts.DSerial, dbo.ChkParts.CheckSerial, dbo.ChkParts.GlobSerial, dbo.ChkParts.PrtNo, dbo.ChkParts.WaiterID_Owner, dbo.Waiters.WaiterName, dbo.ChkParts.SysDate, dbo.ChkParts.ShiftID, 
                         dbo.Shifts.ShiftName, dbo.ChkParts.PCount, dbo.Check_Details.ChDID, dbo.FamilyGroups.FGID, dbo.FamilyGroups.FGCode, dbo.FamilyGroups.FGName, dbo.Families.FID, dbo.Families.FCode, dbo.Families.FName, 
                         dbo.Check_Details.ArtID, dbo.Articles.ArtCode, dbo.Articles.ArtName, dbo.Check_Details.Qty - dbo.Check_Details.RQty AS NetQty, dbo.Check_Details.UnitPrice, dbo.Check_Details.Price - dbo.Check_Details.RPrice AS NetPrice, 
                         dbo.Check_Details.NetAmount, dbo.Check_Details.DeducAmount, dbo.Check_Details.GrossAmount, dbo.Check_Details.Discount, dbo.Check_Details.DueAmount, dbo.Check_Details.OrderDate, dbo.Check_Details.BaseUnitPrice, 
                         dbo.Floors.FloorID, dbo.Floors.FloorName, dbo.ChkParts.Subtotal AS ChkSubtotal, dbo.ChkParts.NetAmount AS ChkNetAmount, dbo.ChkParts.DeducAmount AS ChkDeducAmount, dbo.ChkParts.GrossAmount AS ChkGrossAmount, 
                         dbo.ChkParts.Discount AS ChkDiscount, dbo.ChkParts.DueAmount AS ChkDueAmount, ISNULL(TaxesChk_1.TGTDesc, '') AS ChkTGTDesc_1, ISNULL(TaxesChk_1.Amount, 0) AS ChkTaxesAmount_1, 
                         ISNULL(TaxesChk_2.TGTDesc, '') AS ChkTGTDesc_2, ISNULL(TaxesChk_2.Amount, 0) AS ChkTaxesAmount_2, ISNULL(TaxesChk_3.TGTDesc, '') AS ChkTGTDesc_3, ISNULL(TaxesChk_3.Amount, 0) AS ChkTaxesAmount_3, 
                         ISNULL(TaxesArt_1.TGTDesc, '') AS ArtTGTDesc_1, ISNULL(TaxesArt_1.Amount, 0) AS ArtTaxesAmount_1, ISNULL(TaxesArt_2.TGTDesc, '') AS ArtTGTDesc_2, ISNULL(TaxesArt_2.Amount, 0) AS ArtTaxesAmount_2, 
                         ISNULL(TaxesArt_3.TGTDesc, '') AS ArtTGTDesc_3, ISNULL(TaxesArt_3.Amount, 0) AS ArtTaxesAmount_3, dbo.Check_Details.OTID, dbo.OrderTypes.OTCode, dbo.OrderTypes.OTName, dbo.OrderTypes.NotInSales, 
                         ISNULL(dbo.Units.UnitName, N'') AS UnitName, ISNULL(dbo.Units.UnitShortName, N'') AS UnitShortName, dbo.Check_Details.IsIFCDone
FROM            dbo.Check_Details LEFT OUTER JOIN
                             (SELECT        ID AS ChDID, Amount, TGTDesc
                                FROM            dbo.gTaxes(6, 3) AS gTaxes_Art_3) AS TaxesArt_3 ON dbo.Check_Details.ChDID = TaxesArt_3.ChDID LEFT OUTER JOIN
                             (SELECT        ID AS ChDID, Amount, TGTDesc
                                FROM            dbo.gTaxes(6, 2) AS gTaxes_Art_2) AS TaxesArt_2 ON dbo.Check_Details.ChDID = TaxesArt_2.ChDID LEFT OUTER JOIN
                             (SELECT        ID AS ChDID, Amount, TGTDesc
                                FROM            dbo.gTaxes(6, 1) AS gTaxes_Art_1) AS TaxesArt_1 ON dbo.Check_Details.ChDID = TaxesArt_1.ChDID LEFT OUTER JOIN
                         dbo.Articles LEFT OUTER JOIN
                         dbo.Units ON dbo.Articles.UnitID = dbo.Units.UnitID LEFT OUTER JOIN
                         dbo.FamilyGroups RIGHT OUTER JOIN
                         dbo.Families ON dbo.FamilyGroups.FGID = dbo.Families.FGID ON dbo.Articles.FID = dbo.Families.FID ON dbo.Check_Details.ArtID = dbo.Articles.ArtID LEFT OUTER JOIN
                         dbo.OrderTypes ON dbo.Check_Details.OTID = dbo.OrderTypes.OTID LEFT OUTER JOIN
                             (SELECT        ID AS ChkID, Amount, TGTDesc
                                FROM            dbo.gTaxes(4, 3) AS gTaxes_Chk_3) AS TaxesChk_3 RIGHT OUTER JOIN
                         dbo.Waiters INNER JOIN
                         dbo.ChkParts ON dbo.Waiters.WaiterID = dbo.ChkParts.WaiterID_Owner ON TaxesChk_3.ChkID = dbo.ChkParts.ChkPrtID LEFT OUTER JOIN
                             (SELECT        ID AS ChkID, Amount, TGTDesc
                                FROM            dbo.gTaxes(4, 2) AS gTaxes_Chk_2) AS TaxesChk_2 ON dbo.ChkParts.ChkPrtID = TaxesChk_2.ChkID LEFT OUTER JOIN
                             (SELECT        ID AS ChkID, Amount, TGTDesc
                                FROM            dbo.gTaxes(4, 1) AS gTaxes_Chk_1) AS TaxesChk_1 ON dbo.ChkParts.ChkPrtID = TaxesChk_1.ChkID LEFT OUTER JOIN
                         dbo.Shifts ON dbo.ChkParts.ShiftID = dbo.Shifts.ShiftID LEFT OUTER JOIN
                         dbo.CostCenters LEFT OUTER JOIN
                         dbo.CCGroups ON dbo.CostCenters.CCGID = dbo.CCGroups.CCGID RIGHT OUTER JOIN
                         dbo.Floors RIGHT OUTER JOIN
                         dbo.Tables ON dbo.Floors.FloorID = dbo.Tables.FloorID ON dbo.CostCenters.CCID = dbo.Tables.CCID ON dbo.ChkParts.TblID = dbo.Tables.TblID ON dbo.Check_Details.ChkPrtID = dbo.ChkParts.ChkPrtID
WHERE        (dbo.Check_Details.Qty - dbo.Check_Details.RQty > 0) AND (dbo.ChkParts.Voided = 0) AND (dbo.Check_Details.OTID > 0)
GO
/****** Object:  Table [dbo].[ChkPayments]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ChkPayments](
	[ChkPmntID] [int] IDENTITY(1,1) NOT NULL,
	[ChkPrtID] [int] NULL,
	[PID] [int] NULL,
	[PDate] [datetime] NULL,
	[Amount] [decimal](19, 5) NULL,
	[Given] [decimal](19, 5) NULL,
	[Change] [decimal](19, 5) NULL,
	[CurrID] [int] NULL,
	[Rate] [decimal](19, 5) NULL,
	[CurrAmount] [decimal](19, 5) NULL,
	[WaiterID] [int] NULL,
	[bsCurrID] [int] NULL,
	[Voided] [bit] NULL,
	[VoidDate] [datetime] NULL,
	[Tips] [decimal](19, 5) NULL,
	[Description] [nvarchar](max) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[MOfPayment]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[MOfPayment](
	[PID] [int] IDENTITY(1,1) NOT NULL,
	[PCode] [nvarchar](20) NULL,
	[PName] [nvarchar](100) NULL,
	[Pdesc] [nvarchar](150) NULL,
	[MinAmount] [decimal](19, 5) NULL,
	[AskAmount] [bit] NULL,
	[MustHaveClient] [bit] NULL,
	[PTID] [int] NULL,
	[OpenDrawer] [bit] NULL,
	[PaymentAmount] [decimal](19, 5) NULL,
	[DontDisplayChangeMSG] [bit] NULL,
	[PayAccNo] [nvarchar](50) NULL,
	[AskTip] [bit] NULL,
	[AskDesc] [bit] NULL,
	[IsLimit] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PaymentTypes]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PaymentTypes](
	[PTID] [int] IDENTITY(1,1) NOT NULL,
	[PTName] [nvarchar](50) NULL
) ON [PRIMARY]
GO
/****** Object:  View [dbo].[View_1]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



/*@Fltr*/
CREATE VIEW [dbo].[View_1]
AS
SELECT        dbo.ChkPayments.ChkPrtID, dbo.vOrders.ShiftID, dbo.vOrders.ShiftName, dbo.ChkParts.CheckSerial, dbo.ChkParts.Closed, dbo.ChkParts.Voided, dbo.ChkParts.GuestCount, dbo.vOrders.FloorID, dbo.vOrders.FloorName, 
                         dbo.vOrders.FGName, dbo.vOrders.FGID, dbo.ChkPayments.Tips, dbo.vOrders.Discount, dbo.ChkPayments.Amount, dbo.vOrders.NetAmount, dbo.vOrders.GrossAmount, dbo.vOrders.ChkTaxesAmount_1, 
                         dbo.vOrders.ChkTaxesAmount_2, dbo.MOfPayment.PName, dbo.PaymentTypes.PTName, dbo.PaymentTypes.PTID, dbo.vOrders.SysDate AS dates, dbo.ChkParts.SysDate AS sysDate, dbo.vOrders.BaseUnitPrice, 
                         dbo.vOrders.NetQty
FROM            dbo.ChkParts INNER JOIN
                         dbo.ChkPayments ON dbo.ChkParts.ChkPrtID = dbo.ChkPayments.ChkPrtID INNER JOIN
                         dbo.MOfPayment ON dbo.ChkPayments.PID = dbo.MOfPayment.PID INNER JOIN
                         dbo.PaymentTypes ON dbo.MOfPayment.PTID = dbo.PaymentTypes.PTID INNER JOIN
                         dbo.vOrders ON dbo.ChkParts.ChkPrtID = dbo.vOrders.ChkPrtID
WHERE        (dbo.ChkParts.Closed = 1) AND (dbo.ChkParts.Voided = 0) AND (dbo.ChkPayments.Voided = 0) AND (dbo.ChkParts.CheckSerial = 40)


GO
/****** Object:  View [dbo].[View_Orders]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE VIEW [dbo].[View_Orders]
AS
SELECT        dbo.ChkParts.ChkPrtID, dbo.vOrders.ShiftID, dbo.vOrders.ShiftName, dbo.ChkParts.CheckSerial, dbo.ChkParts.Closed, dbo.ChkParts.Voided, dbo.ChkParts.GuestCount, dbo.vOrders.FloorID, dbo.vOrders.FloorName, 
                         dbo.vOrders.FGName, dbo.vOrders.FGID, dbo.vOrders.Discount, dbo.vOrders.NetAmount, dbo.vOrders.GrossAmount, dbo.vOrders.ChkTaxesAmount_1, dbo.vOrders.ChkTaxesAmount_2, dbo.vOrders.ChkTaxesAmount_3, 
                         dbo.vOrders.SysDate AS dates, dbo.ChkParts.SysDate, dbo.vOrders.BaseUnitPrice, dbo.vOrders.NetQty
FROM            dbo.ChkParts INNER JOIN
                         dbo.vOrders ON dbo.ChkParts.ChkPrtID = dbo.vOrders.ChkPrtID
WHERE        (dbo.ChkParts.Closed = 1) AND (dbo.ChkParts.Voided = 0) AND (dbo.vOrders.NotInSales = 0)

GO
/****** Object:  View [dbo].[View_Payment]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE VIEW [dbo].[View_Payment]
AS
SELECT        dbo.ChkPayments.ChkPrtID, SUM(CASE WHEN dbo.PaymentTypes.PTID = 1 THEN dbo.ChkPayments.Amount ELSE 0 END) AS Cash, SUM(CASE WHEN dbo.PaymentTypes.PTID = 2 THEN dbo.ChkPayments.Amount ELSE 0 END) 
                         AS CR, SUM(CASE WHEN dbo.PaymentTypes.PTID = 3 THEN dbo.ChkPayments.Amount ELSE 0 END) AS Hotel, SUM(CASE WHEN dbo.PaymentTypes.PTID = 4 THEN dbo.ChkPayments.Amount ELSE 0 END) AS CityLedger, 
                         SUM(CASE WHEN dbo.PaymentTypes.PTID = 5 THEN dbo.ChkPayments.Amount ELSE 0 END) AS NotInSales, SUM(CASE WHEN dbo.PaymentTypes.PTID = 6 THEN dbo.ChkPayments.Amount ELSE 0 END) AS Officer, 
                         SUM(CASE WHEN dbo.PaymentTypes.PTID = 7 THEN dbo.ChkPayments.Amount ELSE 0 END) AS Complementary
FROM            dbo.ChkPayments INNER JOIN
                         dbo.MOfPayment ON dbo.ChkPayments.PID = dbo.MOfPayment.PID INNER JOIN
                         dbo.PaymentTypes ON dbo.MOfPayment.PTID = dbo.PaymentTypes.PTID
WHERE        (dbo.ChkPayments.Voided = 0)
GROUP BY dbo.ChkPayments.ChkPrtID
GO
/****** Object:  View [dbo].[View_Tips]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE VIEW [dbo].[View_Tips]
AS
SELECT        ChkPrtID, SUM(Tips) AS Tips
FROM            dbo.ChkPayments
GROUP BY ChkPrtID, Voided
HAVING        (Voided = 0)
GO
/****** Object:  View [dbo].[View_Check]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE VIEW [dbo].[View_Check]
AS
SELECT        dbo.View_Orders.ChkPrtID, dbo.View_Orders.ShiftID, dbo.View_Orders.ShiftName, dbo.View_Orders.CheckSerial, dbo.View_Orders.Closed, dbo.View_Orders.Voided, dbo.View_Orders.GuestCount, dbo.View_Orders.FloorID, 
                         dbo.View_Orders.FloorName, dbo.View_Orders.FGName, dbo.View_Orders.FGID, dbo.View_Tips.Tips, dbo.View_Orders.Discount, dbo.View_Payment.Cash, dbo.View_Payment.CR, dbo.View_Payment.CityLedger, 
                         dbo.View_Payment.NotInSales, dbo.View_Payment.PayMob, dbo.View_Payment.Hotel, dbo.View_Payment.Others, dbo.View_Orders.NetAmount, dbo.View_Orders.GrossAmount, dbo.View_Orders.ChkTaxesAmount_1, 
                         dbo.View_Orders.ChkTaxesAmount_2, dbo.View_Orders.ChkTaxesAmount_3, dbo.View_Orders.dates, dbo.View_Orders.SysDate, dbo.View_Orders.BaseUnitPrice, dbo.View_Orders.NetQty
FROM            dbo.View_Orders INNER JOIN
                         dbo.View_Tips ON dbo.View_Orders.ChkPrtID = dbo.View_Tips.ChkPrtID INNER JOIN
                         dbo.View_Payment ON dbo.View_Orders.ChkPrtID = dbo.View_Payment.ChkPrtID

GO
/****** Object:  Table [dbo].[TKeys]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TKeys](
	[TKeys] [int] NOT NULL,
	[TKeyName] [nvarchar](50) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Log]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Log](
	[Line_No] [int] IDENTITY(1,1) NOT NULL,
	[TKey] [int] NULL,
	[ActionDesc] [nvarchar](200) NULL,
	[ChkID] [int] NULL,
	[Amount] [decimal](19, 5) NULL,
	[WaiterID] [int] NULL,
	[ActionDate] [datetime] NULL,
	[OrderID] [int] NULL,
	[ArtID] [int] NULL,
	[PLU] [nvarchar](20) NULL,
	[Qty] [decimal](19, 5) NULL,
	[UnitPrice] [decimal](19, 5) NULL,
	[Terminal] [nvarchar](80) NULL,
	[DestOrderID] [int] NULL,
	[DiscountID] [int] NULL,
	[Discount] [decimal](19, 5) NULL,
	[GAmount] [decimal](19, 5) NULL,
	[MCAmount] [decimal](19, 5) NULL,
	[GuestCount] [int] NULL,
	[TblNo] [int] NULL,
	[SysDate] [datetime] NULL,
	[MachineNU] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  View [dbo].[V_Log]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE VIEW [dbo].[V_Log]
AS
SELECT     TOP (100) PERCENT dbo.TKeys.TKeyName, dbo.[Log].Line_No, dbo.[Log].TKey, dbo.[Log].ActionDesc, dbo.[Log].ChkID, dbo.[Log].Amount, dbo.[Log].WaiterID, 
                      dbo.[Log].ActionDate, dbo.[Log].OrderID, dbo.[Log].ArtID, dbo.[Log].PLU, dbo.[Log].Qty, dbo.[Log].UnitPrice, dbo.[Log].Terminal, dbo.[Log].DestOrderID, 
                      dbo.[Log].DiscountID, dbo.[Log].Discount, dbo.[Log].GAmount, dbo.[Log].MCAmount, dbo.[Log].GuestCount, dbo.[Log].TblNo
FROM         dbo.[Log] INNER JOIN
                      dbo.TKeys ON dbo.[Log].TKey = dbo.TKeys.TKeys
ORDER BY dbo.[Log].Line_No


GO
/****** Object:  Table [dbo].[Order_ComboDetails]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Order_ComboDetails](
	[CODID] [int] IDENTITY(1,1) NOT NULL,
	[ChkDtlID] [int] NULL,
	[ArtID] [int] NULL,
	[UnitPrice] [decimal](19, 5) NULL,
	[Qty] [decimal](19, 5) NULL
) ON [PRIMARY]
GO
/****** Object:  View [dbo].[vComboDetails]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE VIEW [dbo].[vComboDetails]
AS
SELECT        dbo.Check_Details.ChkPrtID, dbo.Order_ComboDetails.ArtID, dbo.Articles.ArtCode, dbo.Articles.ArtName, dbo.Order_ComboDetails.UnitPrice, dbo.Order_ComboDetails.Qty, dbo.Check_Details.ChDID, 
                         dbo.ChkParts.SysDate, dbo.Check_Details.ArtID AS MainArtID, dbo.Check_Details.Qty - dbo.Check_Details.RQty AS MainNetQty, dbo.OrderTypes.NotInSales AS MainNotInSales, 
                         dbo.OrderTypes.OTID AS MainOTID, dbo.OrderTypes.OTName AS MainOTName
FROM            dbo.Order_ComboDetails INNER JOIN
                         dbo.Articles ON dbo.Order_ComboDetails.ArtID = dbo.Articles.ArtID INNER JOIN
                         dbo.Check_Details ON dbo.Order_ComboDetails.ChkDtlID = dbo.Check_Details.ChDID LEFT OUTER JOIN
                         dbo.OrderTypes ON dbo.Check_Details.OTID = dbo.OrderTypes.OTID LEFT OUTER JOIN
                         dbo.ChkParts ON dbo.Check_Details.ChkPrtID = dbo.ChkParts.ChkPrtID
WHERE        (dbo.Check_Details.Qty - dbo.Check_Details.RQty <> 0)

GO
/****** Object:  Table [dbo].[Modifiers]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Modifiers](
	[ModID] [int] IDENTITY(1,1) NOT NULL,
	[ModCode] [nvarchar](20) NULL,
	[ModName] [nvarchar](50) NULL,
	[ModPrice] [decimal](19, 5) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[OrderModifiers]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[OrderModifiers](
	[OrderModID] [int] IDENTITY(1,1) NOT NULL,
	[ChkDtlID] [int] NULL,
	[ModID] [int] NULL,
	[UnitPrice] [decimal](19, 5) NULL
) ON [PRIMARY]
GO
/****** Object:  View [dbo].[vModifiers]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO





CREATE VIEW [dbo].[vModifiers]
AS
SELECT        dbo.Check_Details.ChkPrtID, dbo.Check_Details.ChDID, dbo.Modifiers.ModID, dbo.Modifiers.ModName, dbo.OrderModifiers.UnitPrice
FROM            dbo.Check_Details INNER JOIN
                         dbo.Articles ON dbo.Check_Details.ArtID = dbo.Articles.ArtID INNER JOIN
                         dbo.OrderModifiers INNER JOIN
                         dbo.Modifiers ON dbo.OrderModifiers.ModID = dbo.Modifiers.ModID ON dbo.Check_Details.ChDID = dbo.OrderModifiers.ChkDtlID




GO
/****** Object:  Table [dbo].[Currencies]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Currencies](
	[CurrID] [int] IDENTITY(1,1) NOT NULL,
	[CurrCode] [nvarchar](5) NULL,
	[ShortName] [nvarchar](20) NULL,
	[CurrName] [nvarchar](50) NULL,
	[Rate] [decimal](19, 5) NULL
) ON [PRIMARY]
GO
/****** Object:  View [dbo].[vPayments]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
 CREATE VIEW [dbo].[vPayments]   AS   SELECT        dbo.CCGroups.CCGID, dbo.CCGroups.CCGCode, dbo.CCGroups.CCGName, dbo.CostCenters.CCID, dbo.CostCenters.CCCode, dbo.CostCenters.CCName,    dbo.ChkParts.ChkPrtID, dbo.ChkParts.TblID,         dbo.Tables.TblCode, dbo.Tables.TblName, dbo.ChkParts.DSerial, dbo.ChkParts.CheckSerial, dbo.ChkParts.GlobSerial, dbo.ChkParts.PrtNo, dbo.ChkParts.WaiterID_Owner, dbo.Waiters.WaiterName,    dbo.ChkParts.SysDate, dbo.ChkParts.ShiftID, dbo.Shifts.ShiftName, dbo.ChkParts.PCount, dbo.Floors.FloorID, dbo.Floors.FloorName, dbo.ChkParts.Subtotal AS ChkSubtotal,    dbo.ChkParts.NetAmount AS ChkNetAmount, dbo.ChkParts.DeducAmount AS ChkDeducAmount, dbo.ChkParts.GrossAmount AS ChkGrossAmount, dbo.ChkParts.Discount AS ChkDiscount,       dbo.ChkParts.DueAmount AS ChkDueAmount, dbo.ChkPayments.ChkPmntID, dbo.ChkPayments.PID, dbo.MOfPayment.PCode, dbo.MOfPayment.PName, dbo.PaymentTypes.PTID, dbo.PaymentTypes.PTName,    dbo.ChkPayments.PDate, dbo.ChkPayments.Amount, dbo.ChkPayments.Given, dbo.ChkPayments.Change, dbo.ChkPayments.CurrID, FCurr.ShortName AS FShortName, FCurr.CurrName AS FCurrName,     dbo.ChkPayments.Rate, dbo.ChkPayments.CurrAmount, dbo.ChkPayments.bsCurrID, LCurr.ShortName, LCurr.CurrName, dbo.ChkPayments.Tips, dbo.ChkPayments.Description   FROM            dbo.PaymentTypes RIGHT OUTER JOIN   dbo.MOfPayment ON dbo.PaymentTypes.PTID = dbo.MOfPayment.PTID RIGHT OUTER JOIN   dbo.ChkPayments ON dbo.MOfPayment.PID = dbo.ChkPayments.PID LEFT OUTER JOIN   dbo.Currencies AS LCurr ON dbo.ChkPayments.bsCurrID = LCurr.CurrID LEFT OUTER JOIN    dbo.Currencies AS FCurr ON dbo.ChkPayments.CurrID = FCurr.CurrID LEFT OUTER JOIN   dbo.Waiters RIGHT OUTER JOIN   dbo.ChkParts ON dbo.Waiters.WaiterID = dbo.ChkParts.WaiterID_Owner ON dbo.ChkPayments.ChkPrtID = dbo.ChkParts.ChkPrtID LEFT OUTER JOIN   dbo.Shifts ON dbo.ChkParts.ShiftID = dbo.Shifts.ShiftID LEFT OUTER JOIN    dbo.CostCenters LEFT OUTER JOIN   dbo.CCGroups ON dbo.CostCenters.CCGID = dbo.CCGroups.CCGID RIGHT OUTER JOIN   dbo.Floors RIGHT OUTER JOIN   dbo.Tables ON dbo.Floors.FloorID = dbo.Tables.FloorID ON dbo.CostCenters.CCID = dbo.Tables.CCID ON dbo.ChkParts.TblID = dbo.Tables.TblID   WHERE        (dbo.ChkParts.Voided = 0) AND (dbo.ChkPayments.Voided = 0)
GO
/****** Object:  Table [dbo].[ChkDeductions]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ChkDeductions](
	[ChkDedID] [int] IDENTITY(1,1) NOT NULL,
	[IDType] [int] NULL,
	[ID] [int] NULL,
	[TaxID] [int] NULL,
	[Amount] [decimal](19, 5) NULL,
	[TGID] [int] NULL,
	[IncDisc] [bit] NULL,
	[FixedAmount] [bit] NULL,
	[Percentage] [decimal](19, 2) NULL,
	[CalcLevel] [int] NULL,
	[Scope] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Taxes]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Taxes](
	[TaxID] [int] IDENTITY(1,1) NOT NULL,
	[TaxCode] [nvarchar](20) NULL,
	[TaxName] [nvarchar](80) NULL,
	[TaxDesc] [nvarchar](100) NULL,
	[FixedAmount] [bit] NULL,
	[Percentage] [decimal](19, 2) NULL,
	[Amount] [decimal](19, 2) NULL,
	[Reduce] [bit] NULL,
	[TGID] [int] NULL,
	[CalcLevel] [int] NULL,
	[Scope] [int] NULL,
	[TGTID] [int] NULL,
	[IncDisc] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[TaxGroupingTypes]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TaxGroupingTypes](
	[TGTID] [int] IDENTITY(1,1) NOT NULL,
	[TGTCode] [nvarchar](20) NULL,
	[TGTDesc] [nvarchar](100) NULL
) ON [PRIMARY]
GO
/****** Object:  View [dbo].[vTaxes_Art]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE VIEW [dbo].[vTaxes_Art]
AS
SELECT        dbo.ChkDeductions.ID AS ChDID, dbo.ChkDeductions.TaxID, dbo.ChkDeductions.Amount, dbo.Taxes.TaxName, dbo.TaxGroupingTypes.TGTID, dbo.TaxGroupingTypes.TGTDesc
FROM            dbo.ChkParts INNER JOIN
                         dbo.Check_Details ON dbo.ChkParts.ChkPrtID = dbo.Check_Details.ChkPrtID INNER JOIN
                         dbo.ChkDeductions ON dbo.Check_Details.ChDID = dbo.ChkDeductions.ID LEFT OUTER JOIN
                         dbo.TaxGroupingTypes RIGHT OUTER JOIN
                         dbo.Taxes ON dbo.TaxGroupingTypes.TGTID = dbo.Taxes.TGTID ON dbo.ChkDeductions.TaxID = dbo.Taxes.TaxID
WHERE        (dbo.ChkDeductions.IDType = 6) AND (dbo.ChkParts.Voided = 0) AND (dbo.Check_Details.Qty - dbo.Check_Details.RQty <> 0)
GO
/****** Object:  View [dbo].[vTaxes_Chk]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE VIEW [dbo].[vTaxes_Chk]
AS
SELECT        dbo.ChkDeductions.ID AS ChkID, dbo.ChkDeductions.TaxID, dbo.ChkDeductions.Amount, dbo.Taxes.TaxName, dbo.TaxGroupingTypes.TGTID, dbo.TaxGroupingTypes.TGTDesc
FROM            dbo.ChkParts RIGHT OUTER JOIN
                         dbo.ChkDeductions ON dbo.ChkParts.ChkPrtID = dbo.ChkDeductions.ID LEFT OUTER JOIN
                         dbo.TaxGroupingTypes RIGHT OUTER JOIN
                         dbo.Taxes ON dbo.TaxGroupingTypes.TGTID = dbo.Taxes.TGTID ON dbo.ChkDeductions.TaxID = dbo.Taxes.TaxID
WHERE        (dbo.ChkDeductions.IDType = 4) AND (dbo.ChkParts.Voided = 0)


GO
/****** Object:  View [dbo].[vTaxesAll]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE VIEW [dbo].[vTaxesAll]
AS
SELECT        AllTaxes.ChkPrtID AS ChkID, AllTaxes.TaxGroupingID, AllTaxes.TaxGroupingDescription, AllTaxes.VATAmount AS TaxAmount, AllTaxes.TaxName, ChkParts_1.SysDate, dbo.Tables.CCID
FROM            (SELECT        dbo.ChkParts.ChkPrtID, dbo.ChkDeductions.TaxID AS VATID, dbo.Taxes.TaxCode AS VATCode, dbo.Taxes.TaxName AS VATName, dbo.Taxes.TaxDesc AS VATDescription, dbo.ChkDeductions.Amount AS VATAmount, 
                                                    dbo.TaxGroupingTypes.TGTID AS TaxGroupingID, dbo.TaxGroupingTypes.TGTDesc AS TaxGroupingDescription, dbo.Taxes.TaxName
                           FROM            dbo.ChkParts INNER JOIN
                                                    dbo.Check_Details ON dbo.ChkParts.ChkPrtID = dbo.Check_Details.ChkPrtID INNER JOIN
                                                    dbo.ChkDeductions INNER JOIN
                                                    dbo.Taxes ON dbo.ChkDeductions.TaxID = dbo.Taxes.TaxID INNER JOIN
                                                    dbo.TaxGroupingTypes ON dbo.Taxes.TGTID = dbo.TaxGroupingTypes.TGTID ON dbo.Check_Details.ChDID = dbo.ChkDeductions.ID
                           WHERE        (dbo.ChkDeductions.IDType = 6)
                           UNION ALL
                           SELECT        ChkDeductions_1.ID AS ChkPrtID, ChkDeductions_1.TaxID AS VATID, Taxes_1.TaxCode AS VATCode, Taxes_1.TaxName AS VATName, Taxes_1.TaxDesc AS VATDescription, ChkDeductions_1.Amount AS VATAmount, 
                                                    TaxGroupingTypes_1.TGTID AS TaxGroupingID, TaxGroupingTypes_1.TGTDesc AS TaxGroupingDescription, Taxes_1.TaxName
                           FROM            dbo.ChkDeductions AS ChkDeductions_1 INNER JOIN
                                                    dbo.Taxes AS Taxes_1 ON ChkDeductions_1.TaxID = Taxes_1.TaxID INNER JOIN
                                                    dbo.TaxGroupingTypes AS TaxGroupingTypes_1 ON Taxes_1.TGTID = TaxGroupingTypes_1.TGTID
                           WHERE        (ChkDeductions_1.IDType = 4)) AS AllTaxes INNER JOIN
                         dbo.ChkParts AS ChkParts_1 ON AllTaxes.ChkPrtID = ChkParts_1.ChkPrtID INNER JOIN
                         dbo.Tables ON ChkParts_1.TblID = dbo.Tables.TblID
WHERE        (ChkParts_1.Voided = 0)
GO
/****** Object:  Table [dbo].[ChkPaymentsReservation]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ChkPaymentsReservation](
	[ChkPmntID] [int] IDENTITY(1,1) NOT FOR REPLICATION NOT NULL,
	[ChkPrtID] [int] NULL,
	[PID] [int] NULL,
	[PDate] [datetime] NULL,
	[Amount] [decimal](19, 5) NULL,
	[Given] [decimal](19, 5) NULL,
	[Change] [decimal](19, 5) NULL,
	[CurrID] [int] NULL,
	[Rate] [decimal](19, 5) NULL,
	[CurrAmount] [decimal](19, 5) NULL,
	[WaiterID] [int] NULL,
	[bsCurrID] [int] NULL,
	[Voided] [bit] NULL,
	[VoidDate] [datetime] NULL,
	[Tips] [decimal](19, 5) NULL,
	[Description] [nvarchar](max) NULL,
	[CheckNu] [int] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ChkPartsReservation]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ChkPartsReservation](
	[ChkPrtID] [int] IDENTITY(1,1) NOT FOR REPLICATION NOT NULL,
	[TblID] [int] NULL,
	[DSerial] [int] NULL,
	[CheckSerial] [int] NULL,
	[GlobSerial] [int] NULL,
	[PrtNo] [int] NULL,
	[Subtotal] [decimal](19, 5) NULL,
	[NetAmount] [decimal](19, 5) NULL,
	[DeducAmount] [decimal](19, 5) NULL,
	[GrossAmount] [decimal](19, 5) NULL,
	[Discount] [decimal](19, 5) NULL,
	[DueAmount] [decimal](19, 5) NULL,
	[DID] [int] NULL,
	[WaiterID_Open] [int] NULL,
	[OpenDate] [datetime] NULL,
	[Closed] [bit] NULL,
	[WaiterID_Close] [int] NULL,
	[ClosingDate] [datetime] NULL,
	[Voided] [bit] NULL,
	[VoidDate] [datetime] NULL,
	[WaiterID_Void] [int] NULL,
	[Printed] [bit] NULL,
	[GuestCount] [int] NULL,
	[MinCharge] [decimal](19, 5) NULL,
	[WaiterID_Owner] [int] NULL,
	[ClientID] [int] NULL,
	[SysDate] [datetime] NULL,
	[ShiftID] [int] NULL,
	[PCount] [int] NULL,
	[ChkNotes] [nvarchar](100) NULL,
	[MachineNU] [int] NULL,
	[IsEmployee] [bit] NULL,
	[IsMember] [bit] NULL,
	[Position] [nvarchar](max) NULL,
	[Location] [nvarchar](max) NULL,
	[CheckNu] [int] NULL,
	[ReservationComment] [nvarchar](max) NULL,
	[ReservationPax] [nvarchar](max) NULL,
	[ReservationDate] [datetime] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  View [dbo].[vPaymentsReservation]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


 CREATE VIEW [dbo].[vPaymentsReservation]   AS   
 SELECT        dbo.CCGroups.CCGID, dbo.CCGroups.CCGCode, dbo.CCGroups.CCGName, dbo.CostCenters.CCID, dbo.CostCenters.CCCode, dbo.CostCenters.CCName,  
 dbo.ChkPartsReservation.ChkPrtID, dbo.ChkPartsReservation.TblID,         dbo.Tables.TblCode, dbo.Tables.TblName, dbo.ChkPartsReservation.DSerial, dbo.ChkPartsReservation.CheckSerial, dbo.ChkPartsReservation.GlobSerial,
 dbo.ChkPartsReservation.PrtNo, dbo.ChkPartsReservation.WaiterID_Owner, dbo.Waiters.WaiterName,    dbo.ChkPartsReservation.SysDate, dbo.ChkPartsReservation.ShiftID, dbo.Shifts.ShiftName, dbo.ChkPartsReservation.PCount, 
 dbo.Floors.FloorID, dbo.Floors.FloorName, dbo.ChkPartsReservation.Subtotal AS ChkSubtotal,    dbo.ChkPartsReservation.NetAmount AS ChkNetAmount, dbo.ChkPartsReservation.DeducAmount AS ChkDeducAmount,
 dbo.ChkPartsReservation.GrossAmount AS ChkGrossAmount, dbo.ChkPartsReservation.Discount AS ChkDiscount,       dbo.ChkPartsReservation.DueAmount AS ChkDueAmount, dbo.ChkPaymentsReservation.ChkPmntID, 
 dbo.ChkPaymentsReservation.PID, dbo.MOfPayment.PCode, dbo.MOfPayment.PName, dbo.PaymentTypes.PTID, dbo.PaymentTypes.PTName,    dbo.ChkPaymentsReservation.PDate, dbo.ChkPaymentsReservation.Amount, 
 dbo.ChkPaymentsReservation.Given, dbo.ChkPaymentsReservation.Change, dbo.ChkPaymentsReservation.CurrID, FCurr.ShortName AS FShortName, FCurr.CurrName AS FCurrName,    
 dbo.ChkPaymentsReservation.Rate, dbo.ChkPaymentsReservation.CurrAmount, dbo.ChkPaymentsReservation.bsCurrID, LCurr.ShortName, LCurr.CurrName, dbo.ChkPaymentsReservation.Tips, dbo.ChkPaymentsReservation.Description  
 FROM            dbo.PaymentTypes RIGHT OUTER JOIN   dbo.MOfPayment ON dbo.PaymentTypes.PTID = dbo.MOfPayment.PTID RIGHT OUTER JOIN  
 dbo.ChkPaymentsReservation ON dbo.MOfPayment.PID = dbo.ChkPaymentsReservation.PID LEFT OUTER JOIN   dbo.Currencies AS LCurr ON dbo.ChkPaymentsReservation.bsCurrID = LCurr.CurrID LEFT OUTER JOIN   
 dbo.Currencies AS FCurr ON dbo.ChkPaymentsReservation.CurrID = FCurr.CurrID LEFT OUTER JOIN   dbo.Waiters RIGHT OUTER JOIN  
 dbo.ChkPartsReservation ON dbo.Waiters.WaiterID = dbo.ChkPartsReservation.WaiterID_Owner ON dbo.ChkPaymentsReservation.ChkPrtID = dbo.ChkPartsReservation.ChkPrtID LEFT OUTER JOIN  
 dbo.Shifts ON dbo.ChkPartsReservation.ShiftID = dbo.Shifts.ShiftID LEFT OUTER JOIN    dbo.CostCenters LEFT OUTER JOIN  
 dbo.CCGroups ON dbo.CostCenters.CCGID = dbo.CCGroups.CCGID RIGHT OUTER JOIN   dbo.Floors RIGHT OUTER JOIN   
 dbo.Tables ON dbo.Floors.FloorID = dbo.Tables.FloorID ON dbo.CostCenters.CCID = dbo.Tables.CCID ON dbo.ChkPartsReservation.TblID = dbo.Tables.TblID   
 WHERE        (dbo.ChkPartsReservation.Voided = 0) AND (dbo.ChkPaymentsReservation.Voided = 0)
GO
/****** Object:  Table [dbo].[Check_DetailsReservation]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Check_DetailsReservation](
	[ChDID] [bigint] IDENTITY(1,1) NOT FOR REPLICATION NOT NULL,
	[ChkPrtID] [int] NULL,
	[ArtID] [int] NULL,
	[Qty] [decimal](19, 5) NULL,
	[UnitPrice] [decimal](19, 5) NULL,
	[Price] [decimal](19, 5) NULL,
	[RQty] [decimal](19, 5) NULL,
	[RPrice] [decimal](19, 5) NULL,
	[Voided] [bit] NULL,
	[VoidDate] [datetime] NULL,
	[WaiterID_Void] [int] NULL,
	[PQty] [decimal](19, 5) NULL,
	[PRQty] [decimal](19, 5) NULL,
	[Printed] [bit] NULL,
	[NetAmount] [decimal](19, 5) NULL,
	[DeducAmount] [decimal](19, 5) NULL,
	[GrossAmount] [decimal](19, 5) NULL,
	[Discount] [decimal](19, 5) NULL,
	[DueAmount] [decimal](19, 5) NULL,
	[DID] [int] NULL,
	[OTID] [int] NULL,
	[TGID] [int] NULL,
	[FreePrice] [bit] NULL,
	[FreeTxt] [bit] NULL,
	[txt] [nvarchar](80) NULL,
	[MemoID] [int] NULL,
	[OrderDate] [datetime] NULL,
	[WaiterID_Order] [int] NULL,
	[DiscPct] [decimal](19, 2) NULL,
	[DiscFixed] [bit] NULL,
	[BaseUnitPrice] [decimal](19, 5) NULL,
	[ArtOrderNotes] [nvarchar](100) NULL,
	[IsIFCDone] [bit] NULL,
	[CheckNu] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[OrderModifiersReservation]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[OrderModifiersReservation](
	[OrderModID] [int] IDENTITY(1,1) NOT FOR REPLICATION NOT NULL,
	[ChkDtlID] [int] NULL,
	[ModID] [int] NULL,
	[UnitPrice] [decimal](19, 5) NULL
) ON [PRIMARY]
GO
/****** Object:  View [dbo].[vModifiersReservation]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO







CREATE VIEW [dbo].[vModifiersReservation]
AS
SELECT        dbo.Check_DetailsReservation.ChkPrtID, dbo.Check_DetailsReservation.ChDID, dbo.Modifiers.ModID, dbo.Modifiers.ModName, dbo.OrderModifiersReservation.UnitPrice
FROM            dbo.Check_DetailsReservation INNER JOIN
                         dbo.Articles ON dbo.Check_DetailsReservation.ArtID = dbo.Articles.ArtID INNER JOIN
                         dbo.OrderModifiersReservation INNER JOIN
                         dbo.Modifiers ON dbo.OrderModifiersReservation.ModID = dbo.Modifiers.ModID ON dbo.Check_DetailsReservation.ChDID = dbo.OrderModifiersReservation.ChkDtlID




GO
/****** Object:  Table [dbo].[Order_ComboDetailsReservation]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Order_ComboDetailsReservation](
	[CODID] [int] IDENTITY(1,1) NOT FOR REPLICATION NOT NULL,
	[ChkDtlID] [int] NULL,
	[ArtID] [int] NULL,
	[UnitPrice] [decimal](19, 5) NULL,
	[Qty] [decimal](19, 5) NULL
) ON [PRIMARY]
GO
/****** Object:  View [dbo].[vComboDetailsReservation]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO




CREATE VIEW [dbo].[vComboDetailsReservation]
AS
SELECT        dbo.Check_DetailsReservation.ChkPrtID, dbo.Order_ComboDetailsReservation.ArtID, dbo.Articles.ArtCode, dbo.Articles.ArtName, dbo.Order_ComboDetailsReservation.UnitPrice, dbo.Order_ComboDetailsReservation.Qty, dbo.Check_DetailsReservation.ChDID, 
                         dbo.ChkPartsReservation.SysDate, dbo.Check_DetailsReservation.ArtID AS MainArtID, dbo.Check_DetailsReservation.Qty - dbo.Check_DetailsReservation.RQty AS MainNetQty, dbo.OrderTypes.NotInSales AS MainNotInSales, 
                         dbo.OrderTypes.OTID AS MainOTID, dbo.OrderTypes.OTName AS MainOTName
FROM            dbo.Order_ComboDetailsReservation INNER JOIN
                         dbo.Articles ON dbo.Order_ComboDetailsReservation.ArtID = dbo.Articles.ArtID INNER JOIN
                         dbo.Check_DetailsReservation ON dbo.Order_ComboDetailsReservation.ChkDtlID = dbo.Check_DetailsReservation.ChDID LEFT OUTER JOIN
                         dbo.OrderTypes ON dbo.Check_DetailsReservation.OTID = dbo.OrderTypes.OTID LEFT OUTER JOIN
                         dbo.ChkPartsReservation ON dbo.Check_DetailsReservation.ChkPrtID = dbo.ChkPartsReservation.ChkPrtID
WHERE        (dbo.Check_DetailsReservation.Qty - dbo.Check_DetailsReservation.RQty <> 0)

GO
/****** Object:  View [dbo].[v_ProcIFC]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE VIEW [dbo].[v_ProcIFC]
AS
SELECT        CCGCode AS mandant, CCCode AS outlet, FloorID AS center, FloorName AS centername, ArtCode AS plu, ArtName AS article, SUM(NetQty) AS Qty, AVG(UnitPrice) AS price, SUM(NetPrice) AS Amount, 
                         SUM(DeducAmount) AS discount, SUM(ChkTaxesAmount_1 + ChkTaxesAmount_2) AS taxamount, SysDate AS statistdate, SUM(CASE (dbo.vOrders.IsIFCDone) WHEN 1 THEN 1 ELSE 0 END) AS IsIFCDone, 
                         0 AS ChDID
FROM            dbo.vOrders
GROUP BY CCGCode, CCCode, FloorID, FloorName, ArtCode, ArtName, SysDate
GO
/****** Object:  Table [dbo].[Activities]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Activities](
	[ActID] [int] IDENTITY(1,1) NOT NULL,
	[ActType] [int] NULL,
	[TblID] [int] NULL,
	[WaiterID] [int] NULL,
	[ChkID] [int] NULL,
	[ActDate] [datetime] NULL,
	[SessionNo] [int] NULL,
	[Flag1] [bit] NULL,
	[Pending] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ArticleComboItems]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ArticleComboItems](
	[CIID] [int] IDENTITY(1,1) NOT NULL,
	[ArtID] [int] NULL,
	[ComboArtID] [int] NULL,
	[ComboGrpID] [int] NULL,
	[UnitPrice] [decimal](19, 5) NULL,
	[Qty] [decimal](19, 5) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ArtMemos]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ArtMemos](
	[MemoID] [int] NOT NULL,
	[ArtID] [int] NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ArtStock]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ArtStock](
	[StcID] [int] IDENTITY(1,1) NOT NULL,
	[CCID] [int] NULL,
	[ArtID] [int] NULL,
	[Stock] [decimal](19, 5) NULL,
	[CreationDate] [datetime] NULL,
	[ModifyDate] [datetime] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[CallsInfo]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CallsInfo](
	[CallID] [bigint] IDENTITY(1,1) NOT NULL,
	[CallNmbr] [varchar](20) NOT NULL,
	[NmbrFormated] [varchar](40) NOT NULL,
	[CallDateTime] [datetime] NOT NULL,
	[Date_Time] [datetime] NOT NULL,
	[BufferID] [bigint] NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[CashDelivery]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CashDelivery](
	[Line_No] [int] IDENTITY(1,1) NOT NULL,
	[CDID] [int] NULL,
	[CurrID] [int] NULL,
	[CoValue] [decimal](19, 5) NULL,
	[CoCount] [decimal](19, 5) NULL,
	[CurrAmount] [decimal](19, 5) NULL,
	[Rate] [decimal](19, 5) NULL,
	[Amount] [decimal](19, 5) NULL,
	[bsCurrID] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[CashDeliveryHeader]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CashDeliveryHeader](
	[CDID] [int] IDENTITY(1,1) NOT NULL,
	[SysDate] [datetime] NOT NULL,
	[ShiftID] [int] NOT NULL,
	[WaiterID] [int] NOT NULL,
	[Indx] [int] NOT NULL,
	[TotShift] [decimal](19, 5) NULL,
	[CD] [decimal](19, 5) NULL,
	[Diff] [decimal](19, 5) NULL,
	[CCID] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[CashMov]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CashMov](
	[TrID] [int] IDENTITY(1,1) NOT NULL,
	[WaiterId] [int] NULL,
	[SysDate] [datetime] NULL,
	[ShiftID] [int] NULL,
	[CashIn] [decimal](19, 5) NULL,
	[CashOut] [decimal](19, 5) NULL,
	[CashDescription] [nvarchar](50) NULL,
	[Date_Time] [datetime] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[CCAcess]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CCAcess](
	[CCAID] [int] IDENTITY(1,1) NOT NULL,
	[WaiterID] [int] NULL,
	[CCID] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[CheckReservationLink]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CheckReservationLink](
	[ID] [int] IDENTITY(1,1) NOT NULL,
	[ChkPrtIDRES] [int] NULL,
	[ChkPrtID] [int] NULL,
 CONSTRAINT [PK_CheckReservationLink] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[CheckVariables]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CheckVariables](
	[VID] [int] IDENTITY(1,1) NOT NULL,
	[TblName] [nvarchar](80) NOT NULL,
	[ClnName] [nvarchar](80) NOT NULL,
	[Tag] [nvarchar](50) NULL,
	[CheckHeader] [bit] NOT NULL,
	[CheckDetails] [bit] NOT NULL,
	[ArticleOrder] [bit] NOT NULL,
	[ComboOrder] [bit] NOT NULL,
	[VoidedArticleOrder] [bit] NOT NULL,
	[ComboVoid] [bit] NOT NULL,
	[Subtotal] [bit] NOT NULL,
	[VATRatesRecord] [bit] NOT NULL,
	[Payments] [bit] NOT NULL,
	[PaymentRecord] [bit] NOT NULL,
	[CheckFooter] [bit] NOT NULL,
	[GroupName] [nvarchar](80) NULL,
	[Caption] [nvarchar](50) NULL,
	[DB] [bit] NULL,
	[Format] [nvarchar](50) NULL,
	[DecimalPlaces] [tinyint] NULL,
	[FullLength] [smallint] NULL,
	[TextAlign] [tinyint] NULL,
	[Active] [bit] NULL,
	[Val] [nvarchar](50) NOT NULL,
	[Flag] [bit] NOT NULL,
	[Client] [bit] NOT NULL,
	[ModifierOrder] [bit] NOT NULL,
	[ModifierVoid] [bit] NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ChkDeductionsReservation]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ChkDeductionsReservation](
	[ChkDedID] [int] IDENTITY(1,1) NOT FOR REPLICATION NOT NULL,
	[IDType] [int] NULL,
	[ID] [int] NULL,
	[TaxID] [int] NULL,
	[Amount] [decimal](19, 5) NULL,
	[TGID] [int] NULL,
	[IncDisc] [bit] NULL,
	[FixedAmount] [bit] NULL,
	[Percentage] [decimal](19, 2) NULL,
	[CalcLevel] [int] NULL,
	[Scope] [int] NULL,
	[CheckNu] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ChkLayouts]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ChkLayouts](
	[LayoutID] [int] NOT NULL,
	[LayoutName] [nvarchar](50) NULL,
	[RptName] [nvarchar](50) NULL,
	[GChk] [bit] NULL,
	[Line_No] [int] IDENTITY(1,1) NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Clients]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Clients](
	[ClientID] [bigint] IDENTITY(1,1) NOT NULL,
	[ClientCode] [nvarchar](25) NULL,
	[ClientPreName] [nvarchar](5) NULL,
	[ClientName] [nvarchar](150) NULL,
	[MobileP] [nvarchar](16) NULL,
	[PhoneH] [nvarchar](16) NULL,
	[City] [nvarchar](50) NULL,
	[Address] [nvarchar](180) NULL,
	[Street] [nvarchar](50) NULL,
	[Badge] [nvarchar](50) NULL,
	[Floor] [nvarchar](50) NULL,
	[Apartment] [nvarchar](50) NULL,
	[RoomNo] [nvarchar](50) NULL,
	[ResNo] [int] NULL,
	[BlackList] [bit] NULL,
	[Complain] [nvarchar](150) NULL,
	[IsEmployee] [bit] NULL,
	[IsMember] [bit] NULL,
	[Limit] [float] NULL,
	[HotelID] [int] NULL,
	[Location] [nvarchar](max) NULL,
	[Position] [nvarchar](max) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ComboGroups]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ComboGroups](
	[ComboGrpID] [int] IDENTITY(1,1) NOT NULL,
	[ArtID] [int] NULL,
	[ComboGrpName] [nvarchar](50) NULL,
	[Prompt] [nvarchar](80) NULL,
	[Priority] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Config]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Config](
	[CfgKey] [int] NOT NULL,
	[POSReport1] [int] NULL,
	[POSReport2] [int] NULL,
	[POSReport3] [int] NULL,
	[POSReport4] [int] NULL,
	[POSReport5] [int] NULL,
	[POSReport6] [int] NULL,
	[POSReport7] [int] NULL,
	[POSReport8] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ConnectDatabaseTBL]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ConnectDatabaseTBL](
	[Id] [int] NULL,
	[ServerName] [nvarchar](max) NULL,
	[UserID] [nvarchar](max) NULL,
	[UserPassword] [nvarchar](max) NULL,
	[DatabaseName] [nvarchar](max) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[CostCenterSummaryTypes]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CostCenterSummaryTypes](
	[TypeIndex] [int] NOT NULL,
	[TypeDescription] [nvarchar](30) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[CurrCoins]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CurrCoins](
	[CoinID] [int] IDENTITY(1,1) NOT NULL,
	[CurrID] [int] NULL,
	[CoNo] [int] NULL,
	[CoName] [nvarchar](50) NULL,
	[CoValue] [decimal](19, 5) NULL,
	[Img] [nvarchar](150) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[dba_indexDefragExclusion]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[dba_indexDefragExclusion](
	[databaseID] [int] NOT NULL,
	[databaseName] [nvarchar](128) NOT NULL,
	[objectID] [int] NOT NULL,
	[objectName] [nvarchar](128) NOT NULL,
	[indexID] [int] NOT NULL,
	[indexName] [nvarchar](128) NOT NULL,
	[exclusionMask] [int] NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[dba_indexDefragLog]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[dba_indexDefragLog](
	[indexDefrag_id] [int] IDENTITY(1,1) NOT NULL,
	[databaseID] [int] NOT NULL,
	[databaseName] [nvarchar](128) NOT NULL,
	[objectID] [int] NOT NULL,
	[objectName] [nvarchar](128) NOT NULL,
	[indexID] [int] NOT NULL,
	[indexName] [nvarchar](128) NOT NULL,
	[partitionNumber] [smallint] NOT NULL,
	[fragmentation] [float] NOT NULL,
	[page_count] [int] NOT NULL,
	[dateTimeStart] [datetime] NOT NULL,
	[dateTimeEnd] [datetime] NULL,
	[durationSeconds] [int] NULL,
	[sqlStatement] [varchar](4000) NULL,
	[errorMessage] [varchar](1000) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[dba_indexDefragStatus]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[dba_indexDefragStatus](
	[databaseID] [int] NOT NULL,
	[databaseName] [nvarchar](128) NOT NULL,
	[objectID] [int] NOT NULL,
	[indexID] [int] NOT NULL,
	[partitionNumber] [smallint] NOT NULL,
	[fragmentation] [float] NOT NULL,
	[page_count] [int] NOT NULL,
	[range_scan_count] [bigint] NOT NULL,
	[schemaName] [nvarchar](128) NULL,
	[objectName] [nvarchar](128) NULL,
	[indexName] [nvarchar](128) NULL,
	[scanDate] [datetime] NOT NULL,
	[defragDate] [datetime] NULL,
	[printStatus] [bit] NOT NULL,
	[exclusionMask] [int] NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[DeliveryActions]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[DeliveryActions](
	[DActionID] [bigint] IDENTITY(1,1) NOT NULL,
	[ChkID] [int] NOT NULL,
	[DBoyID] [int] NOT NULL,
	[WaiterID] [int] NOT NULL,
	[Date_Time] [datetime] NOT NULL,
	[Notes] [nvarchar](200) NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[DeliveryBoys]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[DeliveryBoys](
	[DBoyID] [int] IDENTITY(1,1) NOT NULL,
	[DBoyCode] [nvarchar](16) NULL,
	[DBoyName] [nvarchar](150) NOT NULL,
	[DBoyDrivingLicPlate] [nvarchar](50) NULL,
	[DBoyMobile] [nvarchar](20) NULL,
	[DBoyAddress] [nvarchar](200) NULL,
	[DBoyNotes] [nvarchar](200) NULL,
	[Active] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Devices]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Devices](
	[DeviceID] [int] IDENTITY(1,1) NOT NULL,
	[DeviceCode] [nvarchar](20) NULL,
	[DeviceName] [nvarchar](150) NULL,
	[ConfigFilePath] [text] NULL,
	[Disable] [bit] NULL,
	[WorkStationName] [nvarchar](50) NULL,
	[WinName] [nvarchar](150) NULL,
	[WinPrinter] [bit] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Discounts]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Discounts](
	[DID] [int] IDENTITY(1,1) NOT NULL,
	[DiscCode] [nvarchar](20) NULL,
	[DiscName] [nvarchar](100) NULL,
	[Discount] [decimal](19, 2) NULL,
	[FromGross] [bit] NULL,
	[FixedAmount] [bit] NULL,
	[Amount] [decimal](19, 2) NULL,
	[Scope] [int] NULL,
	[IsNotActive] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[FamilyModifiers]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[FamilyModifiers](
	[FModID] [int] IDENTITY(1,1) NOT NULL,
	[typ] [int] NULL,
	[ID] [int] NULL,
	[ModID] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[FloorClientTBL]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[FloorClientTBL](
	[FloorClientID] [int] IDENTITY(1,1) NOT NULL,
	[FloorID] [int] NULL,
	[IsMember] [bit] NULL,
	[IsEmployee] [bit] NULL,
	[IsRoom] [bit] NULL,
	[IsClient] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[FloorMOFTBL]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[FloorMOFTBL](
	[OrderTypeMOFId] [int] IDENTITY(1,1) NOT NULL,
	[PID] [int] NULL,
	[FloorID] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[HotelsTBL]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[HotelsTBL](
	[HotelID] [int] IDENTITY(1,1) NOT NULL,
	[HotelCode] [int] NULL,
	[HotelName] [nvarchar](max) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IFCProtelServices]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IFCProtelServices](
	[ChkPrtID] [int] NULL,
	[POSDate] [datetime] NULL,
	[IFCDate] [datetime] NULL,
	[Type] [nvarchar](5) NULL,
	[IsDone] [bit] NULL,
	[TB] [int] NULL,
	[IN] [int] NULL,
	[TS] [datetime] NULL,
	[VD] [bit] NULL,
	[SysDate] [datetime] NULL,
	[OL] [int] NULL,
	[Tot] [float] NULL,
	[Ser] [int] IDENTITY(1,1) NOT NULL,
	[Reduce] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IFCServiceAT]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IFCServiceAT](
	[ChkPrtID] [int] NULL,
	[Ser] [int] NULL,
	[PT] [nvarchar](150) NULL,
	[TA] [float] NULL,
	[OL] [int] NULL,
	[SF] [nvarchar](50) NULL,
	[AM] [float] NULL,
	[TX] [nvarchar](50) NULL,
	[TANET] [float] NULL,
	[AMNET] [float] NULL,
	[Serial] [int] IDENTITY(1,1) NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IFCServiceItem]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IFCServiceItem](
	[ChkPrtID] [int] NULL,
	[Ser] [int] NULL,
	[CN] [float] NULL,
	[QU] [float] NULL,
	[AN] [nvarchar](150) NULL,
	[PT] [nvarchar](150) NULL,
	[PG] [nvarchar](50) NULL,
	[MG] [int] NULL,
	[TA] [decimal](18, 2) NULL,
	[SF] [nvarchar](50) NULL,
	[AM] [decimal](18, 2) NULL,
	[TX] [nvarchar](50) NULL,
	[OL] [int] NULL,
	[Serial] [int] IDENTITY(1,1) NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IFCServicePH]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IFCServicePH](
	[ChkPrtID] [int] NULL,
	[Ser] [int] NULL,
	[TA] [float] NULL,
	[UN] [int] NULL,
	[PM] [nvarchar](50) NULL,
	[PN] [nvarchar](100) NULL,
	[SF] [nvarchar](50) NULL,
	[CS] [nvarchar](50) NULL,
	[FA] [float] NULL,
	[RM] [nvarchar](50) NULL,
	[RN] [nvarchar](50) NULL,
	[OL] [int] NULL,
	[Serial] [int] IDENTITY(1,1) NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[imgs]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[imgs](
	[img] [varbinary](50) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[KDS_Tbl]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[KDS_Tbl](
	[KDS_Id] [int] NOT NULL,
	[KDS_JobNu] [bigint] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[LayoutFloor]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[LayoutFloor](
	[LayOutFloorID] [int] IDENTITY(1,1) NOT NULL,
	[FloorID] [int] NULL,
	[LayoutID] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Memos]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Memos](
	[MemoID] [int] IDENTITY(1,1) NOT NULL,
	[MemoName] [nvarchar](50) NULL,
	[Fixed] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[MenuCardDetails]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[MenuCardDetails](
	[MCDetailsID] [int] IDENTITY(1,1) NOT NULL,
	[MCardID] [int] NULL,
	[ArtID] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[MenuCards]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[MenuCards](
	[MCardID] [int] IDENTITY(1,1) NOT NULL,
	[MCardCode] [nvarchar](20) NULL,
	[MCardName] [nvarchar](150) NULL,
	[MCardDesc] [nvarchar](250) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ModemBuffer]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ModemBuffer](
	[BufferID] [bigint] IDENTITY(1,1) NOT NULL,
	[CommLineInfo] [varchar](1000) NOT NULL,
	[ReceivingDate] [datetime] NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[POSReportDetailsTBL]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[POSReportDetailsTBL](
	[POSReportID] [int] IDENTITY(1,1) NOT NULL,
	[RoleID] [int] NULL,
	[ReportId] [int] NULL,
	[ReportName] [nvarchar](150) NULL,
	[IsActive] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[POSReportTBL]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[POSReportTBL](
	[POSReportID] [int] IDENTITY(1,1) NOT NULL,
	[ReportId] [int] NULL,
	[ReportName] [nvarchar](150) NULL,
	[IsActive] [bit] NULL,
	[ReportNameAR] [nvarchar](250) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PriceListGroup]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PriceListGroup](
	[PLGID] [int] IDENTITY(1,1) NOT NULL,
	[PLGCode] [nvarchar](50) NULL,
	[PLGName] [nvarchar](150) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PriceLists]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PriceLists](
	[PLID] [int] IDENTITY(1,1) NOT NULL,
	[PLCode] [nvarchar](50) NULL,
	[PLName] [nvarchar](150) NULL,
	[StartTime] [datetime] NULL,
	[EndTime] [datetime] NULL,
	[Day0] [bit] NULL,
	[Day1] [bit] NULL,
	[Day2] [bit] NULL,
	[Day3] [bit] NULL,
	[Day4] [bit] NULL,
	[Day5] [bit] NULL,
	[Day6] [bit] NULL,
	[PLGID] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Prices]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Prices](
	[PriceID] [int] IDENTITY(1,1) NOT NULL,
	[PLID] [int] NULL,
	[ArtID] [int] NULL,
	[Price] [decimal](19, 5) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Printers]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Printers](
	[PrinterID] [int] IDENTITY(1,1) NOT NULL,
	[PrinterCode] [nvarchar](20) NULL,
	[PrinterName] [nvarchar](150) NULL,
	[SpoolPath] [text] NULL,
	[DeviceID] [int] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Printing]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Printing](
	[PrntTaskID] [int] IDENTITY(1,1) NOT NULL,
	[TaskType] [int] NULL,
	[ID] [int] NULL,
	[PrinterID] [int] NULL,
	[LayoutID] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Reasons]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Reasons](
	[ReasonID] [int] IDENTITY(1,1) NOT NULL,
	[ReasonName] [nvarchar](50) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Regions]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Regions](
	[RegnID] [int] IDENTITY(1,1) NOT NULL,
	[RegnName] [nvarchar](50) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Report]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Report](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[nameAr] [nvarchar](50) NOT NULL,
	[nameEn] [nvarchar](50) NOT NULL,
	[descriptionAr] [nvarchar](350) NOT NULL,
	[descriptionEn] [nvarchar](350) NOT NULL,
	[RptName] [nvarchar](50) NOT NULL,
	[MainReport] [int] NOT NULL,
	[AddWhere] [bit] NOT NULL,
	[RGID] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ReportFilters]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ReportFilters](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[RptID] [int] NOT NULL,
	[RFTCode] [int] NOT NULL,
	[FtrClause] [bit] NOT NULL,
	[Val] [bit] NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ReportFilterTypes]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ReportFilterTypes](
	[RFTCode] [int] NOT NULL,
	[RFTName] [nvarchar](50) NOT NULL,
	[FldName] [nvarchar](50) NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ReportGroup]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ReportGroup](
	[RGID] [int] NOT NULL,
	[RGName] [nvarchar](50) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SysConstants]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SysConstants](
	[CnstKey] [nvarchar](50) NOT NULL,
	[CnstValue] [nvarchar](80) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SysInfo]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SysInfo](
	[SysName] [nvarchar](50) NOT NULL,
	[DBVersion] [nvarchar](50) NULL,
	[AppVersion] [nvarchar](50) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SysLic]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SysLic](
	[LicID] [int] IDENTITY(1,1) NOT NULL,
	[LicKey] [text] NULL,
	[LicDate] [nvarchar](25) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SysRoleAccess]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SysRoleAccess](
	[RoleAccessID] [int] IDENTITY(1,1) NOT NULL,
	[RoleID] [int] NULL,
	[typ] [int] NULL,
	[ID] [int] NULL,
	[Viw] [bit] NULL,
	[Change] [bit] NULL,
	[Del] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SysRoles]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SysRoles](
	[RoleID] [int] IDENTITY(1,1) NOT NULL,
	[RoleName] [nvarchar](80) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SystemDate]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SystemDate](
	[SysDate] [datetime] NOT NULL,
	[StartDate] [datetime] NULL,
	[EndDate] [datetime] NULL,
	[WaiterID] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SysVersionTBL]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SysVersionTBL](
	[ID] [int] NOT NULL,
	[SysVersion] [float] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[TableRanges]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TableRanges](
	[TRID] [int] IDENTITY(1,1) NOT NULL,
	[TRCode] [nvarchar](20) NULL,
	[RangeDesc] [nvarchar](100) NULL,
	[FromNo] [int] NULL,
	[ToNo] [int] NULL,
	[TGID] [int] NULL,
	[CCID] [int] NULL,
	[PLGID] [int] NULL,
	[FloorID] [int] NULL,
	[TblStyleID] [int] NULL,
	[OTID] [int] NULL,
	[MinAmount] [decimal](19, 5) NULL,
	[ChangeTableDisabled] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[TableStyles]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TableStyles](
	[TblStyleID] [int] IDENTITY(1,1) NOT NULL,
	[TblStyleName] [nvarchar](80) NULL,
	[Img] [nvarchar](200) NULL,
	[Fw] [int] NULL,
	[Fh] [int] NULL,
	[ImgBusy] [nvarchar](200) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[TaxGroups]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TaxGroups](
	[TGID] [int] IDENTITY(1,1) NOT NULL,
	[TGCode] [nvarchar](20) NULL,
	[TGDesc] [nvarchar](100) NULL,
	[IncDisc] [bit] NULL,
	[Reduce] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[TaxTasks]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TaxTasks](
	[VATTaskID] [int] IDENTITY(1,1) NOT NULL,
	[TaskType] [int] NULL,
	[ID] [int] NULL,
	[LocationType] [int] NULL,
	[LocationID] [int] NULL,
	[TGID] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Terminals]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Terminals](
	[TermID] [int] IDENTITY(1,1) NOT NULL,
	[TermName] [nvarchar](150) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[TimesTBL]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TimesTBL](
	[ID] [int] IDENTITY(1,1) NOT NULL,
	[Times] [time](7) NULL,
	[ShiftID] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Transfers]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Transfers](
	[TransferID] [int] IDENTITY(1,1) NOT NULL,
	[WaiterID] [int] NULL,
	[ChkID] [int] NULL,
	[TransferDate] [datetime] NULL,
	[ToWaiterID] [int] NULL,
	[Status] [int] NULL,
	[ActionDate] [datetime] NULL,
	[ActionWaiterID] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[TranslateTBL]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TranslateTBL](
	[ID] [int] IDENTITY(1,1) NOT NULL,
	[FormName] [nvarchar](250) NULL,
	[ControlName] [nvarchar](250) NULL,
	[TextEN] [nvarchar](250) NULL,
	[TextAR] [nvarchar](250) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[VoidReasons]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[VoidReasons](
	[VoidReasonID] [int] IDENTITY(1,1) NOT NULL,
	[ChkDtlID] [int] NULL,
	[ReasonID] [int] NULL,
	[VoidQty] [decimal](19, 5) NULL,
	[WaiterID] [int] NULL,
	[VoidDate] [datetime] NULL,
	[VChk] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[VoidRequests]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[VoidRequests](
	[Line_No] [int] IDENTITY(1,1) NOT NULL,
	[WaiterID] [int] NULL,
	[ChDID] [bigint] NULL,
	[Qty] [decimal](19, 2) NULL,
	[ToWaiterID] [int] NULL,
	[Rejected] [bit] NULL,
	[Accepted] [bit] NULL,
	[ActionDate] [datetime] NULL,
	[VoidReason] [nvarchar](50) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[WaiterShifts]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[WaiterShifts](
	[Line_No] [int] IDENTITY(1,1) NOT NULL,
	[SysDate] [datetime] NOT NULL,
	[ShiftID] [int] NOT NULL,
	[WaiterID] [int] NOT NULL,
	[OpenDate] [datetime] NULL,
	[ClosingDate] [datetime] NULL,
	[Closed] [bit] NULL,
	[ClosingWaiterID] [int] NULL,
	[CCID] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[WaiterTeams]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[WaiterTeams](
	[WTeamID] [int] IDENTITY(1,1) NOT NULL,
	[WTeamCode] [nvarchar](50) NULL,
	[WTeamName] [nvarchar](150) NULL,
	[WTeamNotes] [nvarchar](200) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[WOrderDetails]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[WOrderDetails](
	[WOID] [int] NULL,
	[ArtID] [int] NULL,
	[Qty] [decimal](19, 5) NULL,
	[UnitPrice] [decimal](19, 2) NULL,
	[Price] [decimal](19, 2) NULL,
	[Notes] [nvarchar](100) NULL,
	[CrDate] [datetime] NULL,
	[UserID] [int] NULL,
	[Voided] [bit] NULL,
	[Line_No] [int] IDENTITY(1,1) NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[WOrders]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[WOrders](
	[WOID] [int] IDENTITY(1,1) NOT NULL,
	[WID] [int] NULL,
	[ClientID] [int] NULL,
	[CDateTime] [datetime] NULL,
	[Closed] [bit] NULL,
	[Voided] [bit] NULL,
	[StatusDate] [datetime] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[WorkStations]    Script Date: 28/05/2025 10:13 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[WorkStations](
	[WSID] [int] IDENTITY(1,1) NOT NULL,
	[WSName] [nvarchar](80) NULL,
	[WSIP] [varchar](15) NULL
) ON [PRIMARY]
GO
ALTER TABLE [dbo].[Discounts] ADD  CONSTRAINT [DF_Discounts_IsNotActive]  DEFAULT ((0)) FOR [IsNotActive]
GO
ALTER TABLE [dbo].[Floors] ADD  CONSTRAINT [DF_Floors_IsNotActive]  DEFAULT ((0)) FOR [IsNotActive]
GO
ALTER TABLE [dbo].[IFCProtelServices] ADD  CONSTRAINT [DF_IFCProtelServices_Reduce]  DEFAULT ((0)) FOR [Reduce]
GO
ALTER TABLE [dbo].[Tables] ADD  CONSTRAINT [DF_Tables_IsNotActive]  DEFAULT ((0)) FOR [IsNotActive]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPane1', @value=N'[0E232FF0-B466-11cf-A24F-00AA00A3EFFF, 1.00]
Begin DesignProperties = 
   Begin PaneConfigurations = 
      Begin PaneConfiguration = 0
         NumPanes = 4
         Configuration = "(H (1[30] 4[16] 2[19] 3) )"
      End
      Begin PaneConfiguration = 1
         NumPanes = 3
         Configuration = "(H (1 [50] 4 [25] 3))"
      End
      Begin PaneConfiguration = 2
         NumPanes = 3
         Configuration = "(H (1 [50] 2 [25] 3))"
      End
      Begin PaneConfiguration = 3
         NumPanes = 3
         Configuration = "(H (4 [30] 2 [40] 3))"
      End
      Begin PaneConfiguration = 4
         NumPanes = 2
         Configuration = "(H (1 [56] 3))"
      End
      Begin PaneConfiguration = 5
         NumPanes = 2
         Configuration = "(H (2 [66] 3))"
      End
      Begin PaneConfiguration = 6
         NumPanes = 2
         Configuration = "(H (4 [50] 3))"
      End
      Begin PaneConfiguration = 7
         NumPanes = 1
         Configuration = "(V (3))"
      End
      Begin PaneConfiguration = 8
         NumPanes = 3
         Configuration = "(H (1[56] 4[18] 2) )"
      End
      Begin PaneConfiguration = 9
         NumPanes = 2
         Configuration = "(H (1 [75] 4))"
      End
      Begin PaneConfiguration = 10
         NumPanes = 2
         Configuration = "(H (1[66] 2) )"
      End
      Begin PaneConfiguration = 11
         NumPanes = 2
         Configuration = "(H (4 [60] 2))"
      End
      Begin PaneConfiguration = 12
         NumPanes = 1
         Configuration = "(H (1) )"
      End
      Begin PaneConfiguration = 13
         NumPanes = 1
         Configuration = "(V (4))"
      End
      Begin PaneConfiguration = 14
         NumPanes = 1
         Configuration = "(V (2))"
      End
      ActivePaneConfig = 0
   End
   Begin DiagramPane = 
      Begin Origin = 
         Top = 0
         Left = 0
      End
      Begin Tables = 
         Begin Table = "ChkParts"
            Begin Extent = 
               Top = 6
               Left = 38
               Bottom = 136
               Right = 212
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "ChkPayments"
            Begin Extent = 
               Top = 14
               Left = 386
               Bottom = 144
               Right = 556
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "MOfPayment"
            Begin Extent = 
               Top = 15
               Left = 693
               Bottom = 145
               Right = 912
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "PaymentTypes"
            Begin Extent = 
               Top = 0
               Left = 830
               Bottom = 119
               Right = 1000
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "vOrders"
            Begin Extent = 
               Top = 99
               Left = 223
               Bottom = 229
               Right = 417
            End
            DisplayFlags = 280
            TopColumn = 39
         End
      End
   End
   Begin SQLPane = 
   End
   Begin DataPane = 
      Begin ParameterDefaults = ""
      End
      Begin ColumnWidths = 27
         Width = 284
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
 ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'View_1'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPane2', @value=N'        Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
      End
   End
   Begin CriteriaPane = 
      Begin ColumnWidths = 11
         Column = 1440
         Alias = 900
         Table = 1170
         Output = 720
         Append = 1400
         NewValue = 1170
         SortType = 1350
         SortOrder = 1410
         GroupBy = 1350
         Filter = 1350
         Or = 1350
         Or = 1350
         Or = 1350
      End
   End
End
' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'View_1'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPaneCount', @value=2 , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'View_1'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPane1', @value=N'[0E232FF0-B466-11cf-A24F-00AA00A3EFFF, 1.00]
Begin DesignProperties = 
   Begin PaneConfigurations = 
      Begin PaneConfiguration = 0
         NumPanes = 4
         Configuration = "(H (1[36] 4[17] 2[24] 3) )"
      End
      Begin PaneConfiguration = 1
         NumPanes = 3
         Configuration = "(H (1 [50] 4 [25] 3))"
      End
      Begin PaneConfiguration = 2
         NumPanes = 3
         Configuration = "(H (1 [50] 2 [25] 3))"
      End
      Begin PaneConfiguration = 3
         NumPanes = 3
         Configuration = "(H (4 [30] 2 [40] 3))"
      End
      Begin PaneConfiguration = 4
         NumPanes = 2
         Configuration = "(H (1 [56] 3))"
      End
      Begin PaneConfiguration = 5
         NumPanes = 2
         Configuration = "(H (2 [66] 3))"
      End
      Begin PaneConfiguration = 6
         NumPanes = 2
         Configuration = "(H (4 [50] 3))"
      End
      Begin PaneConfiguration = 7
         NumPanes = 1
         Configuration = "(V (3))"
      End
      Begin PaneConfiguration = 8
         NumPanes = 3
         Configuration = "(H (1[56] 4[18] 2) )"
      End
      Begin PaneConfiguration = 9
         NumPanes = 2
         Configuration = "(H (1 [75] 4))"
      End
      Begin PaneConfiguration = 10
         NumPanes = 2
         Configuration = "(H (1[66] 2) )"
      End
      Begin PaneConfiguration = 11
         NumPanes = 2
         Configuration = "(H (4 [60] 2))"
      End
      Begin PaneConfiguration = 12
         NumPanes = 1
         Configuration = "(H (1) )"
      End
      Begin PaneConfiguration = 13
         NumPanes = 1
         Configuration = "(V (4))"
      End
      Begin PaneConfiguration = 14
         NumPanes = 1
         Configuration = "(V (2))"
      End
      ActivePaneConfig = 0
   End
   Begin DiagramPane = 
      Begin Origin = 
         Top = 0
         Left = 0
      End
      Begin Tables = 
         Begin Table = "View_Orders"
            Begin Extent = 
               Top = 6
               Left = 38
               Bottom = 136
               Right = 232
            End
            DisplayFlags = 280
            TopColumn = 16
         End
         Begin Table = "View_Tips"
            Begin Extent = 
               Top = 6
               Left = 268
               Bottom = 102
               Right = 438
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "View_Payment"
            Begin Extent = 
               Top = 6
               Left = 476
               Bottom = 136
               Right = 646
            End
            DisplayFlags = 280
            TopColumn = 4
         End
      End
   End
   Begin SQLPane = 
   End
   Begin DataPane = 
      Begin ParameterDefaults = ""
      End
      Begin ColumnWidths = 29
         Width = 284
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
      End
   End
   Begin CriteriaPane = 
      Begin ColumnWidths = 11
         Column = 1440
         Alias = 900
         Table = 1170
         Output = 720
         Append ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'View_Check'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPane2', @value=N'= 1400
         NewValue = 1170
         SortType = 1350
         SortOrder = 1410
         GroupBy = 1350
         Filter = 1350
         Or = 1350
         Or = 1350
         Or = 1350
      End
   End
End
' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'View_Check'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPaneCount', @value=2 , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'View_Check'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPane1', @value=N'[0E232FF0-B466-11cf-A24F-00AA00A3EFFF, 1.00]
Begin DesignProperties = 
   Begin PaneConfigurations = 
      Begin PaneConfiguration = 0
         NumPanes = 4
         Configuration = "(H (1[30] 4[13] 2[35] 3) )"
      End
      Begin PaneConfiguration = 1
         NumPanes = 3
         Configuration = "(H (1 [50] 4 [25] 3))"
      End
      Begin PaneConfiguration = 2
         NumPanes = 3
         Configuration = "(H (1 [50] 2 [25] 3))"
      End
      Begin PaneConfiguration = 3
         NumPanes = 3
         Configuration = "(H (4 [30] 2 [40] 3))"
      End
      Begin PaneConfiguration = 4
         NumPanes = 2
         Configuration = "(H (1 [56] 3))"
      End
      Begin PaneConfiguration = 5
         NumPanes = 2
         Configuration = "(H (2 [66] 3))"
      End
      Begin PaneConfiguration = 6
         NumPanes = 2
         Configuration = "(H (4 [50] 3))"
      End
      Begin PaneConfiguration = 7
         NumPanes = 1
         Configuration = "(V (3))"
      End
      Begin PaneConfiguration = 8
         NumPanes = 3
         Configuration = "(H (1[56] 4[18] 2) )"
      End
      Begin PaneConfiguration = 9
         NumPanes = 2
         Configuration = "(H (1 [75] 4))"
      End
      Begin PaneConfiguration = 10
         NumPanes = 2
         Configuration = "(H (1[66] 2) )"
      End
      Begin PaneConfiguration = 11
         NumPanes = 2
         Configuration = "(H (4 [60] 2))"
      End
      Begin PaneConfiguration = 12
         NumPanes = 1
         Configuration = "(H (1) )"
      End
      Begin PaneConfiguration = 13
         NumPanes = 1
         Configuration = "(V (4))"
      End
      Begin PaneConfiguration = 14
         NumPanes = 1
         Configuration = "(V (2))"
      End
      ActivePaneConfig = 0
   End
   Begin DiagramPane = 
      Begin Origin = 
         Top = 0
         Left = 0
      End
      Begin Tables = 
         Begin Table = "ChkParts"
            Begin Extent = 
               Top = 0
               Left = 38
               Bottom = 213
               Right = 212
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "vOrders"
            Begin Extent = 
               Top = 0
               Left = 271
               Bottom = 130
               Right = 465
            End
            DisplayFlags = 280
            TopColumn = 53
         End
      End
   End
   Begin SQLPane = 
   End
   Begin DataPane = 
      Begin ParameterDefaults = ""
      End
      Begin ColumnWidths = 26
         Width = 284
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1755
         Width = 1755
         Width = 1980
         Width = 2100
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
      End
   End
   Begin CriteriaPane = 
      Begin ColumnWidths = 11
         Column = 1440
         Alias = 900
         Table = 1170
         Output = 720
         Append = 1400
         NewValue = 1170
         SortType = 1350
         SortOrder = 1410
         GroupBy = 1350
         Filter = 1350
         Or = 1350
         Or = 1350
         Or = 1350
      End
   End
End
' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'View_Orders'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPaneCount', @value=1 , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'View_Orders'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPane1', @value=N'[0E232FF0-B466-11cf-A24F-00AA00A3EFFF, 1.00]
Begin DesignProperties = 
   Begin PaneConfigurations = 
      Begin PaneConfiguration = 0
         NumPanes = 4
         Configuration = "(H (1[32] 4[11] 2[37] 3) )"
      End
      Begin PaneConfiguration = 1
         NumPanes = 3
         Configuration = "(H (1 [50] 4 [25] 3))"
      End
      Begin PaneConfiguration = 2
         NumPanes = 3
         Configuration = "(H (1 [50] 2 [25] 3))"
      End
      Begin PaneConfiguration = 3
         NumPanes = 3
         Configuration = "(H (4 [30] 2 [40] 3))"
      End
      Begin PaneConfiguration = 4
         NumPanes = 2
         Configuration = "(H (1 [56] 3))"
      End
      Begin PaneConfiguration = 5
         NumPanes = 2
         Configuration = "(H (2 [66] 3))"
      End
      Begin PaneConfiguration = 6
         NumPanes = 2
         Configuration = "(H (4 [50] 3))"
      End
      Begin PaneConfiguration = 7
         NumPanes = 1
         Configuration = "(V (3))"
      End
      Begin PaneConfiguration = 8
         NumPanes = 3
         Configuration = "(H (1[56] 4[18] 2) )"
      End
      Begin PaneConfiguration = 9
         NumPanes = 2
         Configuration = "(H (1 [75] 4))"
      End
      Begin PaneConfiguration = 10
         NumPanes = 2
         Configuration = "(H (1[66] 2) )"
      End
      Begin PaneConfiguration = 11
         NumPanes = 2
         Configuration = "(H (4 [60] 2))"
      End
      Begin PaneConfiguration = 12
         NumPanes = 1
         Configuration = "(H (1) )"
      End
      Begin PaneConfiguration = 13
         NumPanes = 1
         Configuration = "(V (4))"
      End
      Begin PaneConfiguration = 14
         NumPanes = 1
         Configuration = "(V (2))"
      End
      ActivePaneConfig = 0
   End
   Begin DiagramPane = 
      Begin Origin = 
         Top = 0
         Left = 0
      End
      Begin Tables = 
         Begin Table = "ChkPayments"
            Begin Extent = 
               Top = 11
               Left = 46
               Bottom = 141
               Right = 216
            End
            DisplayFlags = 280
            TopColumn = 11
         End
         Begin Table = "MOfPayment"
            Begin Extent = 
               Top = 1
               Left = 259
               Bottom = 133
               Right = 478
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "PaymentTypes"
            Begin Extent = 
               Top = 8
               Left = 516
               Bottom = 144
               Right = 686
            End
            DisplayFlags = 280
            TopColumn = 0
         End
      End
   End
   Begin SQLPane = 
   End
   Begin DataPane = 
      Begin ParameterDefaults = ""
      End
      Begin ColumnWidths = 26
         Width = 284
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
      End
   End
   Begin CriteriaPane = 
      Begin ColumnWidths = 12
         Column = 2400
         Alias = 900
         Table = 1170
         Output = 720
         Append = 1400
         NewValue = 1170
         SortType = 1350
       ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'View_Payment'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPane2', @value=N'  SortOrder = 1410
         GroupBy = 1350
         Filter = 1350
         Or = 1350
         Or = 1350
         Or = 1350
      End
   End
End
' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'View_Payment'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPaneCount', @value=2 , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'View_Payment'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPane1', @value=N'[0E232FF0-B466-11cf-A24F-00AA00A3EFFF, 1.00]
Begin DesignProperties = 
   Begin PaneConfigurations = 
      Begin PaneConfiguration = 0
         NumPanes = 4
         Configuration = "(H (1[41] 4[21] 2[16] 3) )"
      End
      Begin PaneConfiguration = 1
         NumPanes = 3
         Configuration = "(H (1 [50] 4 [25] 3))"
      End
      Begin PaneConfiguration = 2
         NumPanes = 3
         Configuration = "(H (1 [50] 2 [25] 3))"
      End
      Begin PaneConfiguration = 3
         NumPanes = 3
         Configuration = "(H (4 [30] 2 [40] 3))"
      End
      Begin PaneConfiguration = 4
         NumPanes = 2
         Configuration = "(H (1 [56] 3))"
      End
      Begin PaneConfiguration = 5
         NumPanes = 2
         Configuration = "(H (2 [66] 3))"
      End
      Begin PaneConfiguration = 6
         NumPanes = 2
         Configuration = "(H (4 [50] 3))"
      End
      Begin PaneConfiguration = 7
         NumPanes = 1
         Configuration = "(V (3))"
      End
      Begin PaneConfiguration = 8
         NumPanes = 3
         Configuration = "(H (1[56] 4[18] 2) )"
      End
      Begin PaneConfiguration = 9
         NumPanes = 2
         Configuration = "(H (1 [75] 4))"
      End
      Begin PaneConfiguration = 10
         NumPanes = 2
         Configuration = "(H (1[66] 2) )"
      End
      Begin PaneConfiguration = 11
         NumPanes = 2
         Configuration = "(H (4 [60] 2))"
      End
      Begin PaneConfiguration = 12
         NumPanes = 1
         Configuration = "(H (1) )"
      End
      Begin PaneConfiguration = 13
         NumPanes = 1
         Configuration = "(V (4))"
      End
      Begin PaneConfiguration = 14
         NumPanes = 1
         Configuration = "(V (2))"
      End
      ActivePaneConfig = 0
   End
   Begin DiagramPane = 
      Begin Origin = 
         Top = 0
         Left = 0
      End
      Begin Tables = 
         Begin Table = "ChkPayments"
            Begin Extent = 
               Top = 6
               Left = 38
               Bottom = 136
               Right = 210
            End
            DisplayFlags = 280
            TopColumn = 11
         End
      End
   End
   Begin SQLPane = 
   End
   Begin DataPane = 
      Begin ParameterDefaults = ""
      End
      Begin ColumnWidths = 9
         Width = 284
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
      End
   End
   Begin CriteriaPane = 
      Begin ColumnWidths = 12
         Column = 1440
         Alias = 900
         Table = 1170
         Output = 720
         Append = 1400
         NewValue = 1170
         SortType = 1350
         SortOrder = 1410
         GroupBy = 1350
         Filter = 1350
         Or = 1350
         Or = 1350
         Or = 1350
      End
   End
End
' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'View_Tips'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPaneCount', @value=1 , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'View_Tips'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPane1', @value=N'[0E232FF0-B466-11cf-A24F-00AA00A3EFFF, 1.00]
Begin DesignProperties = 
   Begin PaneConfigurations = 
      Begin PaneConfiguration = 0
         NumPanes = 4
         Configuration = "(H (1[20] 4[41] 2[20] 3) )"
      End
      Begin PaneConfiguration = 1
         NumPanes = 3
         Configuration = "(H (1 [50] 4 [25] 3))"
      End
      Begin PaneConfiguration = 2
         NumPanes = 3
         Configuration = "(H (1 [50] 2 [25] 3))"
      End
      Begin PaneConfiguration = 3
         NumPanes = 3
         Configuration = "(H (4 [30] 2 [40] 3))"
      End
      Begin PaneConfiguration = 4
         NumPanes = 2
         Configuration = "(H (1 [56] 3))"
      End
      Begin PaneConfiguration = 5
         NumPanes = 2
         Configuration = "(H (2 [66] 3))"
      End
      Begin PaneConfiguration = 6
         NumPanes = 2
         Configuration = "(H (4 [50] 3))"
      End
      Begin PaneConfiguration = 7
         NumPanes = 1
         Configuration = "(V (3))"
      End
      Begin PaneConfiguration = 8
         NumPanes = 3
         Configuration = "(H (1[56] 4[18] 2) )"
      End
      Begin PaneConfiguration = 9
         NumPanes = 2
         Configuration = "(H (1 [75] 4))"
      End
      Begin PaneConfiguration = 10
         NumPanes = 2
         Configuration = "(H (1[66] 2) )"
      End
      Begin PaneConfiguration = 11
         NumPanes = 2
         Configuration = "(H (4 [60] 2))"
      End
      Begin PaneConfiguration = 12
         NumPanes = 1
         Configuration = "(H (1) )"
      End
      Begin PaneConfiguration = 13
         NumPanes = 1
         Configuration = "(V (4))"
      End
      Begin PaneConfiguration = 14
         NumPanes = 1
         Configuration = "(V (2))"
      End
      ActivePaneConfig = 0
   End
   Begin DiagramPane = 
      Begin Origin = 
         Top = 0
         Left = 0
      End
      Begin Tables = 
         Begin Table = "Check_Details"
            Begin Extent = 
               Top = 6
               Left = 38
               Bottom = 136
               Right = 208
            End
            DisplayFlags = 280
            TopColumn = 29
         End
         Begin Table = "TaxesArt_3"
            Begin Extent = 
               Top = 6
               Left = 246
               Bottom = 119
               Right = 416
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "TaxesArt_2"
            Begin Extent = 
               Top = 120
               Left = 246
               Bottom = 233
               Right = 416
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "TaxesArt_1"
            Begin Extent = 
               Top = 138
               Left = 38
               Bottom = 251
               Right = 208
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "Articles"
            Begin Extent = 
               Top = 234
               Left = 246
               Bottom = 364
               Right = 416
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "Units"
            Begin Extent = 
               Top = 252
               Left = 38
               Bottom = 365
               Right = 209
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "FamilyGroups"
            Begin Extent = 
               Top = 366
               Left = 38
               Bottom = 496
               Right = 208
            End
            ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'vOrders'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPane2', @value=N'DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "Families"
            Begin Extent = 
               Top = 366
               Left = 246
               Bottom = 496
               Right = 416
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "OrderTypes"
            Begin Extent = 
               Top = 498
               Left = 38
               Bottom = 628
               Right = 208
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "TaxesChk_3"
            Begin Extent = 
               Top = 498
               Left = 246
               Bottom = 611
               Right = 416
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "Waiters"
            Begin Extent = 
               Top = 612
               Left = 246
               Bottom = 742
               Right = 416
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "ChkParts"
            Begin Extent = 
               Top = 630
               Left = 38
               Bottom = 760
               Right = 212
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "TaxesChk_2"
            Begin Extent = 
               Top = 744
               Left = 250
               Bottom = 857
               Right = 420
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "TaxesChk_1"
            Begin Extent = 
               Top = 762
               Left = 38
               Bottom = 875
               Right = 208
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "Shifts"
            Begin Extent = 
               Top = 858
               Left = 246
               Bottom = 988
               Right = 416
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "CostCenters"
            Begin Extent = 
               Top = 876
               Left = 38
               Bottom = 1006
               Right = 208
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "CCGroups"
            Begin Extent = 
               Top = 990
               Left = 246
               Bottom = 1120
               Right = 416
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "Floors"
            Begin Extent = 
               Top = 1008
               Left = 38
               Bottom = 1138
               Right = 209
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "Tables"
            Begin Extent = 
               Top = 1122
               Left = 247
               Bottom = 1252
               Right = 450
            End
            DisplayFlags = 280
            TopColumn = 0
         End
      End
   End
   Begin SQLPane = 
   End
   Begin DataPane = 
      Begin ParameterDefaults = ""
      End
      Begin ColumnWidths = 9
         Width = 284
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
      End
   End
   Begin CriteriaPane = 
      Begin ColumnWidths = 11
         Column = 4050
         Alias = 900
         Table = 1170
         Output =' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'vOrders'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPane3', @value=N' 720
         Append = 1400
         NewValue = 1170
         SortType = 1350
         SortOrder = 1410
         GroupBy = 1350
         Filter = 1350
         Or = 1350
         Or = 1350
         Or = 1350
      End
   End
End
' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'vOrders'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPaneCount', @value=3 , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'vOrders'
GO
