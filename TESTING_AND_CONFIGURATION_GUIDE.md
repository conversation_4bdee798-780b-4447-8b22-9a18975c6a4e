# Testing and Configuration Guide - Enhanced IFC SCM POS System

## Pre-Implementation Checklist

### 1. Database Backup
Before implementing any changes, create a full backup of your databases:
```sql
-- Backup SCM Database
BACKUP DATABASE [TibaRosee] 
TO DISK = 'C:\Backup\TibaRosee_Backup_BeforeEnhancement.bak'

-- Backup POS Database  
BACKUP DATABASE [Tiba_Pos] 
TO DISK = 'C:\Backup\Tiba_Pos_Backup_BeforeEnhancement.bak'
```

### 2. Environment Preparation
- Ensure SQL Server has sufficient disk space for new tables
- Verify application has necessary permissions
- Test database connectivity from application

## Step-by-Step Implementation

### Step 1: Database Schema Enhancement
1. **Execute Enhanced Schema Script**
   ```sql
   -- Run Enhanced_Database_Schema.sql in SQL Server Management Studio
   -- Connected to TibaRosee database
   ```

2. **Verify New Tables Created**
   ```sql
   SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES 
   WHERE TABLE_NAME IN (
       'Production_Orders',
       'Production_Order_Details', 
       'CostCenter_Priorities',
       'Recipe_Calculation_Cache',
       'Transaction_Processing_Log',
       'Inventory_Alerts',
       'Recipe_Validation_Results'
   )
   ```

3. **Configure Initial Data**
   ```sql
   -- Set up cost center priorities
   INSERT INTO CostCenter_Priorities (CostCenterId, Priority, DeductionRule, MaxDeductionPercentage)
   SELECT CostCenter_Id, 
          ROW_NUMBER() OVER (ORDER BY CostCenter_Name) as Priority,
          6 as DeductionRule, -- Priority-based
          100.00 as MaxDeductionPercentage
   FROM CostCenterTbl 
   WHERE IsSales = 1

   -- Enable auto-production for existing recipe products
   UPDATE ProductsTbl 
   SET AutoProduction = 1, 
       ProductionLeadTime = 30,
       RecipeVersion = 1
   WHERE IsRecipe = 1

   -- Update recipe products with version
   UPDATE Recipe_ProductsTbl 
   SET RecipeVersion = 1, 
       IsOptional = 0
   WHERE Del = 0
   ```

### Step 2: Application Code Integration
1. **Compile New Classes**
   - Ensure all new class files are included in project
   - Build solution to check for compilation errors
   - Resolve any missing references

2. **Update Existing Forms**
   - Replace calls to old transaction processing with Enhanced_Transaction_Manager
   - Update UI to use new features where appropriate

### Step 3: Configuration Testing

#### Test 1: Basic Recipe Recursion
```vb
' Test recursive recipe processing
Dim recipeManager As New Recipe_Manager()
Dim costCenters As New List(Of Integer) From {1} ' Use your actual cost center ID

' Test with a known recipe product
Dim productId As Integer = 1 ' Replace with actual recipe product ID
Dim quantity As Decimal = 1
Dim result = recipeManager.ProcessRecipeRecursive(productId, quantity, costCenters)

Console.WriteLine($"Success: {result.Success}")
Console.WriteLine($"Total Cost: {result.TotalCost}")
Console.WriteLine($"Ingredients: {result.IngredientDeductions.Count}")
Console.WriteLine($"Production Required: {result.ProductionRequired.Count}")
```

#### Test 2: Cost Center Optimization
```vb
' Test cost center selection optimization
Dim costCenterManager As New CostCenter_Manager()
Dim productId As Integer = 1 ' Replace with actual product ID
Dim quantity As Decimal = 10

' Test different deduction rules
Dim rules() As CostCenter_Manager.DeductionRuleType = {
    CostCenter_Manager.DeductionRuleType.Priority,
    CostCenter_Manager.DeductionRuleType.HighestStock,
    CostCenter_Manager.DeductionRuleType.FIFO
}

For Each rule In rules
    Dim selectedCostCenters = costCenterManager.OptimizeCostCenterSelection(productId, quantity, rule)
    Console.WriteLine($"Rule {rule}: Selected {selectedCostCenters.Count} cost centers")
Next
```

#### Test 3: Production Order Creation
```vb
' Test production order creation
Dim productionManager As New Production_Manager()
Dim productId As Integer = 1 ' Recipe product ID
Dim quantity As Decimal = 5
Dim costCenterId As Integer = 1
Dim costCenterName As String = "Main Kitchen"
Dim transactionCode As Integer = 1001
Dim transactionPatch As String = Guid.NewGuid().ToString()

Try
    Dim productionOrder = productionManager.CreateProductionOrder(
        productId, quantity, costCenterId, costCenterName, 
        transactionCode, transactionPatch)
    
    Console.WriteLine($"Production Order Created: {productionOrder.ProductionId}")
    Console.WriteLine($"Status: {productionOrder.Status}")
    Console.WriteLine($"Total Cost: {productionOrder.TotalCost}")
    
    ' Test processing the order
    Dim processed = productionManager.ProcessProductionOrder(productionOrder)
    Console.WriteLine($"Order Processed: {processed}")
    
Catch ex As Exception
    Console.WriteLine($"Error: {ex.Message}")
End Try
```

### Step 4: Integration Testing

#### Test 4: Complete Sales Transaction
```vb
' Test complete enhanced sales transaction
Dim enhancedManager As New Enhanced_Transaction_Manager()

' Create sample sales data
Dim salesData As New DataTable()
salesData.Columns.Add("plu", GetType(String))
salesData.Columns.Add("Qty", GetType(Decimal))
salesData.Columns.Add("price", GetType(Decimal))

' Add test data (use your actual product codes)
salesData.Rows.Add("PROD001", 2, 15.50)
salesData.Rows.Add("RECIPE001", 1, 25.00)

Dim costCenters As New List(Of Integer) From {1, 2} ' Your cost center IDs
Dim result = enhancedManager.ProcessSalesTransaction(salesData, costCenters, DateTime.Now)

Console.WriteLine($"Transaction Success: {result.Success}")
Console.WriteLine($"Transaction Code: {result.TransactionCode}")
Console.WriteLine($"Total Amount: {result.TotalAmount}")
Console.WriteLine($"Items Processed: {result.ProcessedItems.Count}")
Console.WriteLine($"Production Orders: {result.ProductionOrders.Count}")

If result.Warnings.Count > 0 Then
    Console.WriteLine("Warnings:")
    For Each warning In result.Warnings
        Console.WriteLine($"  - {warning}")
    Next
End If
```

## Performance Testing

### Test 5: Load Testing
```vb
' Test with multiple concurrent transactions
Dim tasks As New List(Of Task)
Dim transactionCount As Integer = 10

For i As Integer = 1 To transactionCount
    Dim task = Task.Run(Sub()
        ' Create and process transaction
        Dim manager As New Enhanced_Transaction_Manager()
        Dim testData = CreateTestSalesData()
        Dim testCostCenters As New List(Of Integer) From {1}
        Dim result = manager.ProcessSalesTransaction(testData, testCostCenters, DateTime.Now)
        Console.WriteLine($"Transaction {i}: {result.Success}")
    End Sub)
    tasks.Add(task)
Next

Task.WaitAll(tasks.ToArray())
Console.WriteLine($"Completed {transactionCount} concurrent transactions")
```

### Test 6: Recipe Complexity Testing
```sql
-- Test with complex multi-level recipes
-- Create test recipe hierarchy: Product A -> Product B -> Product C -> Raw Materials

-- Level 1: Raw materials (no recipe)
INSERT INTO ProductsTbl (Product_Code, Product_Name, IsRecipe, Cost_Product)
VALUES ('RAW001', 'Raw Material 1', 0, 5.00),
       ('RAW002', 'Raw Material 2', 0, 3.00)

-- Level 2: Semi-finished product (uses raw materials)
INSERT INTO ProductsTbl (Product_Code, Product_Name, IsRecipe, Cost_Product)
VALUES ('SEMI001', 'Semi-Finished Product', 1, 10.00)

INSERT INTO Recipe_ProductsTbl (Product_Id, Recipe_Product_Id, Recipe_Product_Code, Recipe_Product_Name, UsedQuantity, Cost_Product)
SELECT p1.Product_Id, p2.Product_Id, p2.Product_Code, p2.Product_Name, 2.0, p2.Cost_Product
FROM ProductsTbl p1, ProductsTbl p2
WHERE p1.Product_Code = 'SEMI001' AND p2.Product_Code IN ('RAW001', 'RAW002')

-- Level 3: Finished product (uses semi-finished)
INSERT INTO ProductsTbl (Product_Code, Product_Name, IsRecipe, Cost_Product)
VALUES ('FINISHED001', 'Finished Product', 1, 20.00)

INSERT INTO Recipe_ProductsTbl (Product_Id, Recipe_Product_Id, Recipe_Product_Code, Recipe_Product_Name, UsedQuantity, Cost_Product)
SELECT p1.Product_Id, p2.Product_Id, p2.Product_Code, p2.Product_Name, 1.0, p2.Cost_Product
FROM ProductsTbl p1, ProductsTbl p2
WHERE p1.Product_Code = 'FINISHED001' AND p2.Product_Code = 'SEMI001'
```

## Monitoring and Validation

### Database Monitoring Queries
```sql
-- Monitor production orders
SELECT Status, COUNT(*) as Count
FROM Production_Orders
GROUP BY Status

-- Check recipe calculation performance
SELECT AVG(DATEDIFF(ms, CreatedDate, GETDATE())) as AvgCacheAge
FROM Recipe_Calculation_Cache
WHERE ExpiryDate > GETDATE()

-- Monitor transaction processing
SELECT ProcessingStep, Status, COUNT(*) as Count
FROM Transaction_Processing_Log
WHERE CreatedDate >= DATEADD(day, -1, GETDATE())
GROUP BY ProcessingStep, Status

-- Check inventory alerts
SELECT AlertType, COUNT(*) as Count
FROM Inventory_Alerts
WHERE IsResolved = 0
GROUP BY AlertType
```

### Application Performance Monitoring
```vb
' Add performance monitoring to critical operations
Dim stopwatch As New Stopwatch()
stopwatch.Start()

' Your operation here
Dim result = recipeManager.ProcessRecipeRecursive(productId, quantity, costCenters)

stopwatch.Stop()
Console.WriteLine($"Recipe processing took: {stopwatch.ElapsedMilliseconds}ms")

' Log performance metrics
LogPerformanceMetric("RecipeProcessing", stopwatch.ElapsedMilliseconds, result.Success)
```

## Troubleshooting Common Issues

### Issue 1: Recipe Recursion Timeout
**Symptoms**: Long processing times or timeouts
**Solutions**:
- Check for circular dependencies in recipes
- Increase recursion depth limit if needed
- Optimize database queries with proper indexing

### Issue 2: Production Orders Not Creating
**Symptoms**: No production orders generated when expected
**Solutions**:
- Verify AutoProduction flag is set on recipe products
- Check cost center priorities configuration
- Ensure sufficient permissions for production order creation

### Issue 3: Cost Center Selection Issues
**Symptoms**: Unexpected cost center selection
**Solutions**:
- Review cost center priorities configuration
- Check deduction rule settings
- Verify inventory availability in expected cost centers

### Issue 4: Performance Degradation
**Symptoms**: Slow transaction processing
**Solutions**:
- Monitor database performance and optimize queries
- Check cache hit rates and adjust cache expiry
- Review transaction log sizes and implement cleanup

## Rollback Procedures

If issues arise, follow these rollback steps:

1. **Stop Application**: Ensure no active transactions
2. **Restore Database**: 
   ```sql
   RESTORE DATABASE [TibaRosee] 
   FROM DISK = 'C:\Backup\TibaRosee_Backup_BeforeEnhancement.bak'
   WITH REPLACE
   ```
3. **Revert Code**: Switch back to previous application version
4. **Verify Functionality**: Test basic operations
5. **Document Issues**: Record problems for future resolution

## Success Criteria

The implementation is successful when:
- [ ] All new database tables created without errors
- [ ] Recipe recursion processes correctly for test products
- [ ] Production orders create and process successfully
- [ ] Cost center optimization works as expected
- [ ] Sales transactions process with enhanced features
- [ ] Performance meets or exceeds previous version
- [ ] No data corruption or loss occurs
- [ ] All existing functionality continues to work

## Post-Implementation Tasks

1. **User Training**: Train users on new features
2. **Documentation**: Update user manuals
3. **Monitoring Setup**: Implement ongoing monitoring
4. **Backup Schedule**: Update backup procedures
5. **Performance Baseline**: Establish new performance baselines
