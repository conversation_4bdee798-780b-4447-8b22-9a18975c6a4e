Imports System.Data.SqlClient
Imports System.Collections.Generic
Imports System.Linq

Public Class CostCenterManager
    Private Conn As New Conn_Cls
    
    Public Sub New()
        ' Ensure the CostCenterPriorities table exists
        EnsureCostCenterPrioritiesTableExists()
    End Sub
    
    ''' <summary>
    ''' Create the CostCenterPriorities table if it doesn't exist
    ''' </summary>
    Private Sub EnsureCostCenterPrioritiesTableExists()
        Try
            ' Check if table exists
            Dim tableExists As Boolean = False
            Dim dt As New DataTable()
            dt = Conn.SELECT_TXT("SELECT * FROM sys.tables WHERE name = 'CostCenterPriorities'")
            tableExists = (dt.Rows.Count > 0)
            
            If Not tableExists Then
                ' Create the table
                Dim sql As String = "CREATE TABLE [dbo].[CostCenterPriorities]("
                sql += "[ID] [int] IDENTITY(1,1) NOT NULL,"
                sql += "[CostCenter_Id] [int] NOT NULL,"
                sql += "[Priority] [int] NOT NULL,"
                sql += "[IsDefault] [bit] NOT NULL,"
                sql += "[Description] [nvarchar](255) NULL,"
                sql += "[CreatedDate] [datetime] NOT NULL,"
                sql += "[ModifiedDate] [datetime] NULL,"
                sql += "CONSTRAINT [PK_CostCenterPriorities] PRIMARY KEY CLUSTERED ([ID] ASC)"
                sql += ") ON [PRIMARY]"
                
                Conn.EXECUT_Txt(sql)
                
                ' Add default constraints
                sql = "ALTER TABLE [dbo].[CostCenterPriorities] ADD CONSTRAINT [DF_CostCenterPriorities_Priority] DEFAULT ((999)) FOR [Priority]"
                Conn.EXECUT_Txt(sql)
                
                sql = "ALTER TABLE [dbo].[CostCenterPriorities] ADD CONSTRAINT [DF_CostCenterPriorities_IsDefault] DEFAULT ((0)) FOR [IsDefault]"
                Conn.EXECUT_Txt(sql)
                
                sql = "ALTER TABLE [dbo].[CostCenterPriorities] ADD CONSTRAINT [DF_CostCenterPriorities_CreatedDate] DEFAULT (getdate()) FOR [CreatedDate]"
                Conn.EXECUT_Txt(sql)
            End If
        Catch ex As Exception
            Console.WriteLine($"Error ensuring CostCenterPriorities table exists: {ex.Message}")
        End Try
    End Sub
    
    ''' <summary>
    ''' Set the priority for a cost center
    ''' </summary>
    Public Sub SetCostCenterPriority(costCenterId As Integer, priority As Integer, isDefault As Boolean, description As String)
        Try
            ' Check if entry already exists
            Dim dt As New DataTable()
            dt = Conn.SELECT_TXT($"SELECT * FROM CostCenterPriorities WHERE CostCenter_Id = {costCenterId}")
            
            If dt.Rows.Count > 0 Then
                ' Update existing entry
                Dim sql As String = $"UPDATE CostCenterPriorities SET "
                sql += $"Priority = {priority}, "
                sql += $"IsDefault = '{(If(isDefault, 1, 0))}', "
                sql += $"Description = '{description}', "
                sql += $"ModifiedDate = GETDATE() "
                sql += $"WHERE CostCenter_Id = {costCenterId}"
                
                Conn.EXECUT_Txt(sql)
            Else
                ' Insert new entry
                Dim sql As String = $"INSERT INTO CostCenterPriorities (CostCenter_Id, Priority, IsDefault, Description, CreatedDate) "
                sql += $"VALUES ({costCenterId}, {priority}, '{(If(isDefault, 1, 0))}', '{description}', GETDATE())"
                
                Conn.EXECUT_Txt(sql)
            End If
            
            ' If this is set as default, clear other defaults
            If isDefault Then
                Dim sql As String = $"UPDATE CostCenterPriorities SET IsDefault = 0 "
                sql += $"WHERE CostCenter_Id <> {costCenterId}"
                
                Conn.EXECUT_Txt(sql)
            End If
        Catch ex As Exception
            Console.WriteLine($"Error setting cost center priority: {ex.Message}")
        End Try
    End Sub
    
    ''' <summary>
    ''' Get the priority for a cost center
    ''' </summary>
    Public Function GetCostCenterPriority(costCenterId As Integer) As Integer
        Dim priority As Integer = 999 ' Default priority (lowest)
        
        Try
            Dim dt As New DataTable()
            dt = Conn.SELECT_TXT($"SELECT Priority FROM CostCenterPriorities WHERE CostCenter_Id = {costCenterId}")
            
            If dt.Rows.Count > 0 Then
                priority = Convert.ToInt32(dt.Rows(0)("Priority"))
            End If
        Catch ex As Exception
            Console.WriteLine($"Error getting cost center priority: {ex.Message}")
        End Try
        
        Return priority
    End Function
    
    ''' <summary>
    ''' Get the default cost center
    ''' </summary>
    Public Function GetDefaultCostCenter() As Integer
        Dim costCenterId As Integer = 0
        
        Try
            Dim dt As New DataTable()
            dt = Conn.SELECT_TXT("SELECT CostCenter_Id FROM CostCenterPriorities WHERE IsDefault = 1")
            
            If dt.Rows.Count > 0 Then
                costCenterId = Convert.ToInt32(dt.Rows(0)("CostCenter_Id"))
            End If
        Catch ex As Exception
            Console.WriteLine($"Error getting default cost center: {ex.Message}")
        End Try
        
        Return costCenterId
    End Function
    
    ''' <summary>
    ''' Get all cost centers with their priorities
    ''' </summary>
    Public Function GetAllCostCenterPriorities() As DataTable
        Dim result As New DataTable()
        
        Try
            Dim sql As String = "SELECT cc.CostCenter_Id, cc.CostCenter_Name, "
            sql += "ISNULL(cp.Priority, 999) AS Priority, "
            sql += "ISNULL(cp.IsDefault, 0) AS IsDefault, "
            sql += "cp.Description "
            sql += "FROM CostCenterLinkPOS cc "
            sql += "LEFT JOIN CostCenterPriorities cp ON cc.CostCenter_Id = cp.CostCenter_Id "
            sql += "ORDER BY cp.Priority, cc.CostCenter_Id"
            
            result = Conn.SELECT_TXT(sql)
        Catch ex As Exception
            Console.WriteLine($"Error getting all cost center priorities: {ex.Message}")
        End Try
        
        Return result
    End Function
End Class
