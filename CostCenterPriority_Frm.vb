Imports System.Data
Imports System.Windows.Forms

Public Class CostCenterPriority_Frm
    Private CostCenterMgr As New CostCenterManager
    Private Conn As New Conn_Cls
    
    Public Sub New()
        InitializeComponent()
        LoadCostCenters()
    End Sub
    
    Private Sub LoadCostCenters()
        Try
            ' Get all cost centers with their priorities
            Dim dt As DataTable = CostCenterMgr.GetAllCostCenterPriorities()
            
            ' Bind to grid
            dgvCostCenters.DataSource = dt
            
            ' Format grid
            FormatGrid()
            
            ' Load cost centers into combo box
            cmbCostCenter.DataSource = dt.Copy()
            cmbCostCenter.DisplayMember = "CostCenter_Name"
            cmbCostCenter.ValueMember = "CostCenter_Id"
        Catch ex As Exception
            MessageBox.Show($"Error loading cost centers: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub FormatGrid()
        ' Set column headers and formatting
        dgvCostCenters.Columns("CostCenter_Id").HeaderText = "ID"
        dgvCostCenters.Columns("CostCenter_Name").HeaderText = "Cost Center Name"
        dgvCostCenters.Columns("Priority").HeaderText = "Priority"
        dgvCostCenters.Columns("IsDefault").HeaderText = "Default"
        dgvCostCenters.Columns("Description").HeaderText = "Description"
        
        ' Set column widths
        dgvCostCenters.Columns("CostCenter_Id").Width = 50
        dgvCostCenters.Columns("CostCenter_Name").Width = 200
        dgvCostCenters.Columns("Priority").Width = 80
        dgvCostCenters.Columns("IsDefault").Width = 80
        dgvCostCenters.Columns("Description").Width = 200
        
        ' Format boolean column
        dgvCostCenters.Columns("IsDefault").DefaultCellStyle.Format = "Yes;No;No"
    End Sub
    
    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Try
            If cmbCostCenter.SelectedIndex = -1 Then
                MessageBox.Show("Please select a cost center", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If
            
            Dim costCenterId As Integer = Convert.ToInt32(cmbCostCenter.SelectedValue)
            Dim priority As Integer = Convert.ToInt32(nudPriority.Value)
            Dim isDefault As Boolean = chkDefault.Checked
            Dim description As String = txtDescription.Text
            
            ' Save priority
            CostCenterMgr.SetCostCenterPriority(costCenterId, priority, isDefault, description)
            
            ' Refresh grid
            LoadCostCenters()
            
            ' Clear form
            ClearForm()
            
            MessageBox.Show("Cost center priority saved successfully", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            MessageBox.Show($"Error saving cost center priority: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub ClearForm()
        cmbCostCenter.SelectedIndex = -1
        nudPriority.Value = 999
        chkDefault.Checked = False
        txtDescription.Text = ""
    End Sub
    
    Private Sub dgvCostCenters_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles dgvCostCenters.CellClick
        If e.RowIndex >= 0 Then
            ' Get selected row
            Dim row As DataGridViewRow = dgvCostCenters.Rows(e.RowIndex)
            
            ' Populate form
            cmbCostCenter.SelectedValue = row.Cells("CostCenter_Id").Value
            nudPriority.Value = Convert.ToInt32(row.Cells("Priority").Value)
            chkDefault.Checked = Convert.ToBoolean(row.Cells("IsDefault").Value)
            txtDescription.Text = row.Cells("Description").Value.ToString()
        End If
    End Sub
    
    Private Sub btnClear_Click(sender As Object, e As EventArgs) Handles btnClear.Click
        ClearForm()
    End Sub
    
    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        Me.Close()
    End Sub
End Class
