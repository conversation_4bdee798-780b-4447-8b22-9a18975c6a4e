USE [TibaRosee]
GO
/****** Object:  Table [dbo].[Transaction_DetailsTbl]    Script Date: 29/05/2025 03:04 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Transaction_DetailsTbl](
	[Transaction_Code] [int] NULL,
	[Product_Id] [int] NULL,
	[Product_Code] [nvarchar](50) NULL,
	[Product_Name] [nvarchar](150) NULL,
	[Order_Q] [float] NULL,
	[Reciving_Q] [float] NULL,
	[Invoice_Q] [float] NULL,
	[Return_Q] [float] NULL,
	[Cost_Product] [float] NULL,
	[CostTotalLine] [float] NULL,
	[CostCenter_Id_Frm] [int] NULL,
	[CostCenter_Name_Frm] [nvarchar](150) NULL,
	[CostCenter_Id_To] [int] NULL,
	[CostCenter_Name_To] [nvarchar](150) NULL,
	[Suppliers_Id_Frm] [int] NULL,
	[Suppliers_Name_Frm] [nvarchar](150) NULL,
	[Suppliers_Id_To] [int] NULL,
	[Suppliers_Name_To] [nvarchar](150) NULL,
	[Supplier_Frm] [bit] NULL,
	[Supplier_To] [bit] NULL,
	[Patch_Ser] [int] NULL,
	[Patch_Name] [nvarchar](150) NULL,
	[IsExpire] [bit] NULL,
	[Unt_Id] [int] NULL,
	[Unt_Name] [nvarchar](50) NULL,
	[Unt_GroupId] [int] NULL,
	[Unt_Q] [float] NULL,
	[Current_Unt_Id] [int] NULL,
	[Current_Unt_Name] [nvarchar](50) NULL,
	[Current_Unt_Q] [float] NULL,
	[NetQ_Qsetup_CurrentQ] [float] NULL,
	[Transaction_Id] [int] NULL,
	[Transaction_Date_Create] [datetime] NULL,
	[Transaction_Save] [bit] NULL,
	[Transaction_Submit] [bit] NULL,
	[Transaction_Cancel] [bit] NULL,
	[Del] [bit] NULL,
	[Ser] [int] IDENTITY(1,1) NOT NULL,
	[Authulized] [bit] NULL,
	[Period_Store_Id] [int] NULL,
	[Period_Close_Id] [int] NULL,
	[Inventory] [bit] NULL,
	[SOH] [float] NULL,
	[GardDate] [datetime] NULL,
	[Open_Q] [float] NULL,
	[Close_Q] [float] NULL,
	[AvCost] [float] NULL,
	[Invoice_NoReciving] [nvarchar](70) NULL,
	[IsRecipe] [bit] NULL,
	[StockTakeMaster] [int] NULL,
	[InStock] [bit] NULL,
	[Transaction_Patch] [uniqueidentifier] NULL,
	[ReOpen] [bit] NULL,
	[Tax_Id] [int] NULL,
	[Tax_Name] [nvarchar](100) NULL,
	[Tax_Persentage] [float] NULL,
	[Tax_Value] [float] NULL,
	[TaxEffect] [bit] NULL,
	[Cost_ProductPerUnit] [float] NULL,
	[NetQ_Qsetup_CurrentQBase] [float] NULL,
	[NetTotal] [float] NULL,
	[ReturnVariance] [float] NULL,
	[Total_Av] [float] NULL,
	[IsDescount] [bit] NULL,
	[Accept_Reciving] [bit] NULL,
	[Product_Id_PRO] [int] NULL,
	[Auto_Transfer] [bit] NULL,
	[SalesPrice] [float] NULL,
	[TotalSales] [float] NULL,
	[Currency_ID] [int] NULL,
	[Currency_Rate] [float] NULL,
	[TahmelCost_Persent] [float] NULL,
	[TahmelCost_Tota] [float] NULL,
	[TahmelCost_PerUnit] [float] NULL,
	[TahmelCost] [float] NULL,
	[Discount_Amount] [float] NULL,
	[Discount_Pers] [float] NULL,
	[IsGift] [bit] NULL,
	[ShiftID] [int] NULL,
	[FreeSales] [bit] NULL,
	[HasDisc] [bit] NULL,
	[GLCostID] [int] NULL,
	[GLCostIDFrom] [int] NULL,
	[GLCostCode] [nvarchar](max) NULL,
	[GLCostCodeFrom] [nvarchar](max) NULL,
 CONSTRAINT [PK_Transaction_DetailsTbl] PRIMARY KEY CLUSTERED 
(
	[Ser] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Recipe_ProductsTbl]    Script Date: 29/05/2025 03:04 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Recipe_ProductsTbl](
	[Ser] [int] IDENTITY(1,1) NOT NULL,
	[Product_Id] [int] NULL,
	[Product_Name] [nvarchar](150) NULL,
	[Product_Code] [nvarchar](50) NULL,
	[Recipe_Product_Id] [int] NULL,
	[Recipe_Product_Name] [nvarchar](150) NULL,
	[Recipe_Product_Code] [nvarchar](50) NULL,
	[Unt_Id] [int] NULL,
	[Unt_Name] [nvarchar](50) NULL,
	[Unt_GroupId] [int] NULL,
	[Unt_Q] [int] NULL,
	[Current_Unt_Id] [int] NULL,
	[Current_Unt_Name] [nvarchar](50) NULL,
	[Current_Unt_Q] [int] NULL,
	[Cost_Product] [real] NULL,
	[UsedQuantity] [real] NULL,
	[Recipe_Quantity] [real] NULL,
	[Recipe_Price] [real] NULL,
	[Recipe_Persentage_Loss] [real] NULL,
	[Recipe_Price_Loss] [real] NULL,
	[Recipe_Lost_cost] [real] NULL,
	[NetQ_Qsetup_CurrentQ] [real] NULL,
	[Del] [bit] NULL,
	[Cost_ProductPerUnit] [float] NULL,
	[NetQ_Qsetup_CurrentQBase] [float] NULL
) ON [PRIMARY]
GO
/****** Object:  View [dbo].[ProductionDetails]    Script Date: 29/05/2025 03:04 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE VIEW [dbo].[ProductionDetails]
AS
SELECT dbo.Transaction_DetailsTbl.Transaction_Code, dbo.Transaction_DetailsTbl.Product_Id, dbo.Transaction_DetailsTbl.Product_Code, dbo.Transaction_DetailsTbl.Product_Name, dbo.Transaction_DetailsTbl.Reciving_Q, 
                  dbo.Transaction_DetailsTbl.Cost_Product, dbo.Transaction_DetailsTbl.CostTotalLine, dbo.Transaction_DetailsTbl.CostCenter_Name_Frm, dbo.Transaction_DetailsTbl.CostCenter_Name_To, 
                  dbo.Transaction_DetailsTbl.NetQ_Qsetup_CurrentQ, dbo.Transaction_DetailsTbl.Current_Unt_Name, dbo.Transaction_DetailsTbl.SOH, dbo.Transaction_DetailsTbl.Transaction_Patch, dbo.Transaction_DetailsTbl.Transaction_Id, 
                  dbo.Recipe_ProductsTbl.Product_Id AS Production_Id
FROM     dbo.Transaction_DetailsTbl INNER JOIN
                  dbo.Recipe_ProductsTbl ON dbo.Transaction_DetailsTbl.Product_Id = dbo.Recipe_ProductsTbl.Recipe_Product_Id AND dbo.Transaction_DetailsTbl.Product_Id_PRO = dbo.Recipe_ProductsTbl.Product_Id
WHERE  (dbo.Transaction_DetailsTbl.Transaction_Id = 7) AND (dbo.Transaction_DetailsTbl.NetQ_Qsetup_CurrentQ < 0)
GO
/****** Object:  Table [dbo].[Transaction_HeadTbl]    Script Date: 29/05/2025 03:04 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Transaction_HeadTbl](
	[Transaction_Code] [bigint] NOT NULL,
	[CostCenter_Id] [int] NULL,
	[Suppliers_Id] [int] NULL,
	[CostCenter_Supplier] [nvarchar](150) NULL,
	[Remarks] [nvarchar](150) NULL,
	[Invoice_No] [nvarchar](50) NULL,
	[Amount_Bill] [float] NULL,
	[Tax_Bill] [float] NULL,
	[Total_Amount] [float] NULL,
	[Transaction_Save] [bit] NULL,
	[Transaction_Submit] [bit] NULL,
	[User_Id] [int] NULL,
	[Utholization_Id] [int] NULL,
	[Transaction_PaidAmount] [float] NULL,
	[Transaction_NetAmount] [float] NULL,
	[Authulized] [bit] NULL,
	[Ser] [int] IDENTITY(1,1) NOT NULL,
	[Transaction_Id] [int] NULL,
	[Transaction_Date] [datetime] NULL,
	[Transaction_Date_Submit] [datetime] NULL,
	[Transaction_Patch] [uniqueidentifier] ROWGUIDCOL  NULL,
	[Accept_Reciving] [bit] NULL,
	[User_Id_Submit] [bit] NULL,
	[User_IdAccept] [int] NULL,
	[Invoice_NoReciving] [nvarchar](70) NULL,
	[per_no] [int] NULL,
	[Year_Num] [int] NULL,
	[ReOpen] [bit] NULL,
	[AutolizationLevel] [int] NULL,
	[AcceptedLevel] [bit] NULL,
	[DiscardLevel] [bit] NULL,
	[Auto_Transfer] [bit] NULL,
	[User_Id_Discard] [int] NULL,
	[Currency_ID] [int] NULL,
	[Currency_Rate] [float] NULL,
	[TahmelAmount] [float] NULL,
	[Discount_Amount] [float] NULL,
	[Discount_Pers] [float] NULL,
	[ShiftID] [int] NULL,
 CONSTRAINT [PK_Transaction_Head] PRIMARY KEY CLUSTERED 
(
	[Ser] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  View [dbo].[ProductionMaster]    Script Date: 29/05/2025 03:04 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE VIEW [dbo].[ProductionMaster]
AS
SELECT        dbo.Transaction_DetailsTbl.Transaction_Code, dbo.Transaction_DetailsTbl.Product_Id, dbo.Transaction_DetailsTbl.Product_Code, dbo.Transaction_DetailsTbl.Product_Name, 
                         dbo.Transaction_DetailsTbl.Reciving_Q, dbo.Transaction_DetailsTbl.Cost_Product, dbo.Transaction_DetailsTbl.CostTotalLine, dbo.Transaction_DetailsTbl.CostCenter_Name_Frm, 
                         dbo.Transaction_DetailsTbl.CostCenter_Name_To, dbo.Transaction_DetailsTbl.NetQ_Qsetup_CurrentQ, dbo.Transaction_DetailsTbl.Current_Unt_Name, dbo.Transaction_DetailsTbl.SOH, 
                         dbo.Transaction_DetailsTbl.Transaction_Patch, dbo.Transaction_DetailsTbl.Transaction_Id, dbo.UsersTbl.User_Name, dbo.Transaction_HeadTbl.Transaction_Date, 
                         dbo.Recipe_ProductsTbl.Product_Id AS Production_Id
FROM            dbo.Transaction_DetailsTbl INNER JOIN
                         dbo.Transaction_HeadTbl ON dbo.Transaction_DetailsTbl.Transaction_Patch = dbo.Transaction_HeadTbl.Transaction_Patch INNER JOIN
                         dbo.UsersTbl ON dbo.Transaction_HeadTbl.User_Id = dbo.UsersTbl.User_Id INNER JOIN
                         dbo.Recipe_ProductsTbl ON dbo.Transaction_DetailsTbl.Product_Id = dbo.Recipe_ProductsTbl.Product_Id
GROUP BY dbo.Transaction_DetailsTbl.Transaction_Code, dbo.Transaction_DetailsTbl.Product_Id, dbo.Transaction_DetailsTbl.Product_Code, dbo.Transaction_DetailsTbl.Product_Name, 
                         dbo.Transaction_DetailsTbl.Reciving_Q, dbo.Transaction_DetailsTbl.Cost_Product, dbo.Transaction_DetailsTbl.CostTotalLine, dbo.Transaction_DetailsTbl.CostCenter_Name_Frm, 
                         dbo.Transaction_DetailsTbl.CostCenter_Name_To, dbo.Transaction_DetailsTbl.NetQ_Qsetup_CurrentQ, dbo.Transaction_DetailsTbl.Current_Unt_Name, dbo.Transaction_DetailsTbl.SOH, 
                         dbo.Transaction_DetailsTbl.Transaction_Patch, dbo.Transaction_DetailsTbl.Transaction_Id, dbo.UsersTbl.User_Name, dbo.Transaction_HeadTbl.Transaction_Date, dbo.Recipe_ProductsTbl.Product_Id
HAVING        (dbo.Transaction_DetailsTbl.Transaction_Id = 7) AND (dbo.Transaction_DetailsTbl.NetQ_Qsetup_CurrentQ > 0)
GO
/****** Object:  Table [dbo].[CostCenterLinkPOS]    Script Date: 29/05/2025 03:04 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CostCenterLinkPOS](
	[Ser] [int] NULL,
	[CostCenter_Id] [int] NULL,
	[CostCenter_Name] [nvarchar](150) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[CostCenterTbl]    Script Date: 29/05/2025 03:04 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CostCenterTbl](
	[CostCenter_Id] [int] IDENTITY(1,1) NOT NULL,
	[CostCenter_Name] [nvarchar](150) NOT NULL,
	[Store_id] [int] NULL,
	[Store_Name] [nvarchar](150) NULL,
	[Type_id] [int] NULL,
	[CostCenter_Type] [nvarchar](100) NULL,
	[Auto_Transfer] [bit] NULL,
	[IsSales] [bit] NULL,
	[Abbreviation] [nvarchar](50) NULL,
 CONSTRAINT [PK_CostCenterTbl] PRIMARY KEY CLUSTERED 
(
	[CostCenter_Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[CostCenterType]    Script Date: 29/05/2025 03:04 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CostCenterType](
	[Type_id] [int] NOT NULL,
	[CostCenter_Type] [nvarchar](50) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ItmCostCenterLink]    Script Date: 29/05/2025 03:04 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ItmCostCenterLink](
	[Ser] [int] IDENTITY(1,1) NOT NULL,
	[CostCenter_Id] [int] NULL,
	[CostCenter_Name] [nvarchar](150) NULL,
	[Product_Id] [int] NULL,
	[Product_Name] [nvarchar](150) NULL,
	[Product_Code] [nvarchar](50) NULL,
	[MaxiMum] [float] NULL,
	[MinMum] [float] NULL,
	[ReOrder] [float] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Patches]    Script Date: 29/05/2025 03:04 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Patches](
	[Patch_Ser] [int] IDENTITY(1,1) NOT NULL,
	[Patch_Name] [nvarchar](150) NULL,
	[Product_Code] [nvarchar](50) NULL,
	[Product_Name] [nvarchar](150) NULL,
	[CostCenter_Id] [int] NULL,
	[CostCenter_Name] [nvarchar](150) NULL,
	[Unt_Id] [int] NULL,
	[Unt_Name] [nvarchar](50) NULL,
	[Unt_GroupId] [int] NULL,
	[Unt_Q] [real] NULL,
	[Current_Unt_Id] [int] NULL,
	[Current_Unt_Name] [nvarchar](50) NULL,
	[Current_Unt_Q] [real] NULL,
	[NetQ_Qsetup_CurrentQ] [real] NULL,
	[Prud_Date] [datetime] NULL,
	[Exp_Date] [datetime] NULL,
	[CloseOpen] [bit] NULL,
	[usd] [bit] NULL,
	[Product_Id] [int] NULL,
 CONSTRAINT [PK_Patches] PRIMARY KEY CLUSTERED 
(
	[Patch_Ser] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Period_Close]    Script Date: 29/05/2025 03:04 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Period_Close](
	[ser] [int] IDENTITY(1,1) NOT NULL,
	[per_no] [int] NULL,
	[per_frm] [datetime] NULL,
	[per_to] [datetime] NULL,
	[Product_Code] [varchar](50) NULL,
	[Product_Name] [nvarchar](150) NULL,
	[itm_opn] [float] NULL,
	[itm_cls] [float] NULL,
	[clos_dat] [datetime] NULL,
	[clos_tim] [time](7) NULL,
	[per_ser] [int] NULL,
	[CostCenter_Id] [int] NULL,
	[cls_opn] [bit] NULL,
	[nam] [nvarchar](250) NULL,
	[Product_Id] [int] NULL,
	[PeriodClose_Ser] [int] NULL,
	[AvCost] [float] NULL,
	[Cost_Product] [float] NULL,
	[Year_Num] [int] NULL,
	[StockTack_Master_id] [bigint] NULL,
	[SumAvrage] [decimal](18, 3) NULL,
	[SumCost] [decimal](18, 3) NULL,
	[SumAvrageOpen] [decimal](18, 3) NULL,
	[SumCostOpen] [decimal](18, 3) NULL,
 CONSTRAINT [PK_Period_Close] PRIMARY KEY CLUSTERED 
(
	[ser] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Period_Store]    Script Date: 29/05/2025 03:04 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Period_Store](
	[ser] [int] IDENTITY(1,1) NOT NULL,
	[Store_id] [int] NULL,
	[Store_Name] [nvarchar](150) NULL,
	[Month_No] [int] NULL,
	[From_Period] [datetime] NULL,
	[To_Priod] [datetime] NULL,
	[Year_Num] [int] NULL,
	[Period_Numm] [int] NULL,
	[prod] [bit] NULL,
	[CostCenter_Id] [int] NULL,
	[CostCenter_Name] [nvarchar](150) NULL,
	[HasHalf] [bit] NULL,
 CONSTRAINT [PK_Period_Store] PRIMARY KEY CLUSTERED 
(
	[ser] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[POSSalesRevenu]    Script Date: 29/05/2025 03:04 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[POSSalesRevenu](
	[Ser] [int] IDENTITY(1,1) NOT NULL,
	[Product_Id] [int] NULL,
	[Product_Name] [nvarchar](150) NULL,
	[Product_Code] [nvarchar](50) NULL,
	[Unt_Name] [nvarchar](150) NULL,
	[Depart_Id] [int] NULL,
	[Group_Id] [int] NULL,
	[SubGroup_Id] [int] NULL,
	[Cost_Product] [float] NULL,
	[AvCost] [float] NULL,
	[SalesPrice] [float] NULL,
	[NetQ_Qsetup_CurrentQ] [float] NULL,
	[Tax_Amount] [float] NULL,
	[Tax_Name] [nvarchar](50) NULL,
	[TotalCost_Product] [float] NULL,
	[TotalAvCost] [float] NULL,
	[TotalSalesPrice] [float] NULL,
	[NetCost_Product] [float] NULL,
	[NetAvCost] [float] NULL,
	[Transaction_Code] [int] NULL,
	[Transaction_Id] [int] NULL,
	[Transaction_Patch] [uniqueidentifier] NULL,
	[TransDate] [datetime] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[POSSetting]    Script Date: 29/05/2025 03:04 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[POSSetting](
	[Ser] [int] NULL,
	[Company_IdSCM] [int] NULL,
	[Company_NameSCM] [nvarchar](150) NULL,
	[Branch_IdSCM] [int] NULL,
	[Branch_NameSCM] [nvarchar](150) NULL,
	[Store_IdSCM] [int] NULL,
	[Store_NameSCM] [nvarchar](150) NULL,
	[Company_IdPOS] [int] NULL,
	[Company_NamePOS] [nvarchar](150) NULL,
	[Brand_IdPOS] [int] NULL,
	[Brand_NamePOS] [nvarchar](150) NULL,
	[CostCenter_IdPOS] [int] NULL,
	[CostCenter_NamePOS] [nvarchar](150) NULL,
	[IsDelete] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[POSTrans]    Script Date: 29/05/2025 03:04 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[POSTrans](
	[ItemCode] [nvarchar](50) NULL,
	[ItemName] [nvarchar](150) NULL,
	[Quantity] [float] NULL,
	[SalesPrice] [float] NULL,
	[Total] [float] NULL,
	[CostCenter_Id] [int] NULL,
	[Trans_Id] [int] NULL,
	[DateTrans] [datetime] NULL,
	[Patch_Ser] [int] NULL,
	[Patch_Name] [nvarchar](200) NULL,
	[Tax_Amount] [float] NULL,
	[Tax_Name] [nvarchar](50) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ProductsTbl]    Script Date: 29/05/2025 03:04 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ProductsTbl](
	[Product_Id] [int] IDENTITY(1,1) NOT NULL,
	[Product_Name] [nvarchar](150) NOT NULL,
	[Product_Code] [nvarchar](50) NOT NULL,
	[Product_BrandId] [int] NULL,
	[Product_BrandName] [nvarchar](150) NULL,
	[Unt_Id] [int] NULL,
	[Unt_Name] [nvarchar](150) NULL,
	[Unt_GroupId] [int] NULL,
	[Unt_Q] [int] NULL,
	[Depart_Id] [int] NULL,
	[Group_Id] [int] NULL,
	[SubGroup_Id] [int] NULL,
	[Cost_Product] [float] NULL,
	[AvCost] [float] NULL,
	[Consumption] [real] NULL,
	[SalesPrice] [float] NULL,
	[MinStock] [real] NULL,
	[MaxStock] [real] NULL,
	[ReOrder] [real] NULL,
	[Notes] [nvarchar](max) NULL,
	[IsStock] [bit] NULL,
	[IsRecipe] [bit] NULL,
	[IsExpire] [bit] NULL,
	[IsProduction] [bit] NULL,
	[IsSales] [bit] NULL,
	[Auth] [int] NULL,
	[Tax_Id] [int] NULL,
	[Unt_IdSales] [int] NULL,
	[Unt_NameSales] [nvarchar](150) NULL,
	[Unt_QSales] [int] NULL,
	[IsDescount] [bit] NULL,
	[Language] [int] NULL,
	[Title] [nvarchar](200) NULL,
	[Author] [int] NULL,
	[Format] [nvarchar](200) NULL,
	[Edition] [nvarchar](200) NULL,
	[YearOfPublication] [nvarchar](50) NULL,
	[Publisher] [int] NULL,
	[Supplier] [int] NULL,
	[Imprint] [int] NULL,
	[Category] [nvarchar](200) NULL,
	[Translator] [nvarchar](250) NULL,
	[CreatedDate] [datetime] NULL,
 CONSTRAINT [PK_ProductsTbl_1] PRIMARY KEY CLUSTERED 
(
	[Product_Code] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Sales_POS]    Script Date: 29/05/2025 03:04 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Sales_POS](
	[Ser] [int] IDENTITY(1,1) NOT NULL,
	[CostCenterPOS_Id] [int] NULL,
	[CostCenterPOS_Name] [nvarchar](150) NULL,
	[CostCenter_Id_To] [int] NULL,
	[CostCenter_Name_To] [nvarchar](150) NULL,
	[Transaction_Code] [int] NULL,
	[Product_Id] [int] NULL,
	[Product_Code] [nvarchar](50) NULL,
	[Product_Name] [nvarchar](150) NULL,
	[Reciving_Q] [float] NULL,
	[Cost_Product] [float] NULL,
	[CostTotalLine] [float] NULL,
	[Patch_Ser] [int] NULL,
	[Patch_Name] [nvarchar](150) NULL,
	[Unt_Id] [int] NULL,
	[Unt_Name] [nvarchar](50) NULL,
	[Unt_GroupId] [int] NULL,
	[Unt_Q] [float] NULL,
	[Current_Unt_Id] [int] NULL,
	[Current_Unt_Name] [nvarchar](50) NULL,
	[Current_Unt_Q] [float] NULL,
	[NetQ_Qsetup_CurrentQ] [float] NULL,
	[Transaction_Id] [int] NULL,
	[Transaction_Date_Create] [datetime] NULL,
	[Transaction_Submit] [bit] NULL,
	[TransactionDetails_Ser] [int] NULL,
	[Authulized] [bit] NULL,
	[SOH] [float] NULL,
	[Open_Q] [float] NULL,
	[Close_Q] [float] NULL,
	[Transaction_Patch] [uniqueidentifier] NULL,
	[Check_No] [int] NULL,
	[TotalAvg] [float] NULL,
	[Is_Recipy] [bit] NULL,
	[Is_Production] [bit] NULL,
	[CompanyPOS_Id] [int] NULL,
	[CompanyPOS_Name] [nvarchar](150) NULL,
	[OutLetPOS_Id] [int] NULL,
	[OutLetPOS_Name] [nvarchar](150) NULL,
	[MethodOfPayment_Id] [int] NULL,
	[MethodOfPayment_Name] [nvarchar](150) NULL,
	[Sales_Price] [float] NULL,
	[IsExpire] [bit] NULL,
	[ProductionCode] [int] NULL,
	[ChDID] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[StockOnHandTbl]    Script Date: 29/05/2025 03:04 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[StockOnHandTbl](
	[Ser] [int] IDENTITY(1,1) NOT NULL,
	[Product_Id] [int] NULL,
	[Product_Name] [nvarchar](150) NULL,
	[Product_Code] [nvarchar](50) NULL,
	[CostCenter_Id] [int] NULL,
	[Quntity] [decimal](24, 4) NULL,
	[QuntityBase] [decimal](24, 4) NULL,
	[Item_Unit] [real] NULL,
	[AvCost] [float] NULL,
	[Cost_Product] [float] NULL,
	[ReturnVariance] [float] NULL,
	[NetCost] [float] NULL,
	[IsDescount] [bit] NULL,
	[TheAvCost] [float] NULL,
	[TheQuantaty] [float] NULL,
	[SalesPrice] [float] NULL,
 CONSTRAINT [PK_StockOnHandTbl] PRIMARY KEY CLUSTERED 
(
	[Ser] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[StockOnHandTbl_POS]    Script Date: 29/05/2025 03:04 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[StockOnHandTbl_POS](
	[Ser] [int] IDENTITY(1,1) NOT NULL,
	[Product_Id] [int] NULL,
	[Product_Name] [nvarchar](150) NULL,
	[Product_Code] [nvarchar](50) NULL,
	[CostCenter_Id] [int] NULL,
	[Quntity] [decimal](24, 4) NULL,
	[ProcessingContext] [nvarchar](50) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[TransactionsTbl]    Script Date: 29/05/2025 03:04 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TransactionsTbl](
	[Transaction_Id] [int] NOT NULL,
	[Transaction_Name] [varchar](50) NOT NULL,
	[Effecting] [bit] NULL,
	[Pos] [bit] NULL,
 CONSTRAINT [PK_TransactionsTbl] PRIMARY KEY CLUSTERED 
(
	[Transaction_Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
ALTER TABLE [dbo].[CostCenterTbl] ADD  CONSTRAINT [DF_CostCenterTbl_Auto_Transfer]  DEFAULT ((0)) FOR [Auto_Transfer]
GO
ALTER TABLE [dbo].[CostCenterTbl] ADD  CONSTRAINT [DF_CostCenterTbl_IsSales]  DEFAULT ((0)) FOR [IsSales]
GO
ALTER TABLE [dbo].[ItmCostCenterLink] ADD  CONSTRAINT [DF_ItmCostCenterLink_MaxiMum]  DEFAULT ((0)) FOR [MaxiMum]
GO
ALTER TABLE [dbo].[ItmCostCenterLink] ADD  CONSTRAINT [DF_ItmCostCenterLink_MinMum]  DEFAULT ((0)) FOR [MinMum]
GO
ALTER TABLE [dbo].[ItmCostCenterLink] ADD  CONSTRAINT [DF_ItmCostCenterLink_ReOrder]  DEFAULT ((0)) FOR [ReOrder]
GO
ALTER TABLE [dbo].[POSSetting] ADD  CONSTRAINT [DF_POSSetting_IsDelete]  DEFAULT ((0)) FOR [IsDelete]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_Product_BrandId]  DEFAULT ((1)) FOR [Product_BrandId]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_IsStock]  DEFAULT ((1)) FOR [IsStock]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_Tax_Id]  DEFAULT ((1)) FOR [Tax_Id]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_IsDescount]  DEFAULT ((0)) FOR [IsDescount]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_Language]  DEFAULT ((0)) FOR [Language]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_Title]  DEFAULT ('') FOR [Title]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_Author]  DEFAULT ((0)) FOR [Author]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_Edition]  DEFAULT ('') FOR [Edition]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_YearOfPublication]  DEFAULT ((0)) FOR [YearOfPublication]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_Publisher]  DEFAULT ((0)) FOR [Publisher]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_Supplier]  DEFAULT ((0)) FOR [Supplier]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_Imprint]  DEFAULT ((0)) FOR [Imprint]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_Category]  DEFAULT ('') FOR [Category]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_CreatedDate]  DEFAULT (getdate()) FOR [CreatedDate]
GO
ALTER TABLE [dbo].[Sales_POS] ADD  CONSTRAINT [DF_Sales_POS_ChDID]  DEFAULT ((0)) FOR [ChDID]
GO
ALTER TABLE [dbo].[StockOnHandTbl] ADD  CONSTRAINT [DF_StockOnHandTbl_Quntity]  DEFAULT ((0)) FOR [Quntity]
GO
ALTER TABLE [dbo].[StockOnHandTbl] ADD  CONSTRAINT [DF_StockOnHandTbl_QuntityBase]  DEFAULT ((0)) FOR [QuntityBase]
GO
ALTER TABLE [dbo].[StockOnHandTbl] ADD  CONSTRAINT [DF_StockOnHandTbl_Item_Unit]  DEFAULT ((0)) FOR [Item_Unit]
GO
ALTER TABLE [dbo].[StockOnHandTbl] ADD  CONSTRAINT [DF_StockOnHandTbl_ReturnVariance]  DEFAULT ((0)) FOR [ReturnVariance]
GO
ALTER TABLE [dbo].[StockOnHandTbl] ADD  CONSTRAINT [DF_StockOnHandTbl_NetCost]  DEFAULT ((0)) FOR [NetCost]
GO
ALTER TABLE [dbo].[StockOnHandTbl] ADD  CONSTRAINT [DF_StockOnHandTbl_IsDescount]  DEFAULT ((0)) FOR [IsDescount]
GO
ALTER TABLE [dbo].[StockOnHandTbl] ADD  CONSTRAINT [DF_StockOnHandTbl_SalesPrice]  DEFAULT ((0)) FOR [SalesPrice]
GO
ALTER TABLE [dbo].[StockOnHandTbl_POS] ADD  DEFAULT ('DIRECT') FOR [ProcessingContext]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_InStock]  DEFAULT ((1)) FOR [InStock]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_ReOpen]  DEFAULT ((0)) FOR [ReOpen]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_Tax_Id]  DEFAULT ((1)) FOR [Tax_Id]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_Tax_Name]  DEFAULT (N'0 % Tax') FOR [Tax_Name]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_Tax_Persentage]  DEFAULT ((0)) FOR [Tax_Persentage]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_Tax_Value]  DEFAULT ((0)) FOR [Tax_Value]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_TaxEffect]  DEFAULT ((0)) FOR [TaxEffect]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_ReturnVariance]  DEFAULT ((0)) FOR [ReturnVariance]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_Total_Av]  DEFAULT ((0)) FOR [Total_Av]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_IsDescount]  DEFAULT ((0)) FOR [IsDescount]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_Accept_Reciving]  DEFAULT ((0)) FOR [Accept_Reciving]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_Auto_Transfer]  DEFAULT ((1)) FOR [Auto_Transfer]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_SalesPrice]  DEFAULT ((0)) FOR [SalesPrice]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_TotalSales]  DEFAULT ((0)) FOR [TotalSales]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_Currency_ID]  DEFAULT ((1)) FOR [Currency_ID]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_Currency_Rate]  DEFAULT ((1)) FOR [Currency_Rate]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_TahmelCost_Persent]  DEFAULT ((0)) FOR [TahmelCost_Persent]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_TahmelCost_Tota]  DEFAULT ((0)) FOR [TahmelCost_Tota]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_TahmelCost_PerUnit]  DEFAULT ((0)) FOR [TahmelCost_PerUnit]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_TahmelCost]  DEFAULT ((0)) FOR [TahmelCost]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_Discount_Amount]  DEFAULT ((0)) FOR [Discount_Amount]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_Discount_Pers]  DEFAULT ((0)) FOR [Discount_Pers]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_IsGift]  DEFAULT ((0)) FOR [IsGift]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl]  DEFAULT ((0)) FOR [ShiftID]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_FreeSales]  DEFAULT ((0)) FOR [FreeSales]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_HasDisc]  DEFAULT ((0)) FOR [HasDisc]
GO
ALTER TABLE [dbo].[Transaction_HeadTbl] ADD  CONSTRAINT [DF_Transaction_Head_Transaction_Patch]  DEFAULT (newid()) FOR [Transaction_Patch]
GO
ALTER TABLE [dbo].[Transaction_HeadTbl] ADD  CONSTRAINT [DF_Transaction_HeadTbl_ReOpen]  DEFAULT ((0)) FOR [ReOpen]
GO
ALTER TABLE [dbo].[Transaction_HeadTbl] ADD  CONSTRAINT [DF_Transaction_HeadTbl_AutolizationLevel]  DEFAULT ((0)) FOR [AutolizationLevel]
GO
ALTER TABLE [dbo].[Transaction_HeadTbl] ADD  CONSTRAINT [DF_Transaction_HeadTbl_AcceptedLevel]  DEFAULT ((0)) FOR [AcceptedLevel]
GO
ALTER TABLE [dbo].[Transaction_HeadTbl] ADD  CONSTRAINT [DF_Transaction_HeadTbl_DiscardLevel]  DEFAULT ((0)) FOR [DiscardLevel]
GO
ALTER TABLE [dbo].[Transaction_HeadTbl] ADD  CONSTRAINT [DF_Transaction_HeadTbl_Auto_Transfer]  DEFAULT ((1)) FOR [Auto_Transfer]
GO
ALTER TABLE [dbo].[Transaction_HeadTbl] ADD  CONSTRAINT [DF_Transaction_HeadTbl_User_Id_Discard]  DEFAULT ((0)) FOR [User_Id_Discard]
GO
ALTER TABLE [dbo].[Transaction_HeadTbl] ADD  CONSTRAINT [DF_Transaction_HeadTbl_Currency_ID]  DEFAULT ((1)) FOR [Currency_ID]
GO
ALTER TABLE [dbo].[Transaction_HeadTbl] ADD  CONSTRAINT [DF_Transaction_HeadTbl_Currency_Rate]  DEFAULT ((1)) FOR [Currency_Rate]
GO
ALTER TABLE [dbo].[Transaction_HeadTbl] ADD  CONSTRAINT [DF_Transaction_HeadTbl_TahmelAmount]  DEFAULT ((0)) FOR [TahmelAmount]
GO
ALTER TABLE [dbo].[Transaction_HeadTbl] ADD  CONSTRAINT [DF_Transaction_HeadTbl_Discount_Amount]  DEFAULT ((0)) FOR [Discount_Amount]
GO
ALTER TABLE [dbo].[Transaction_HeadTbl] ADD  CONSTRAINT [DF_Transaction_HeadTbl_Discount_Pers]  DEFAULT ((0)) FOR [Discount_Pers]
GO
ALTER TABLE [dbo].[Transaction_HeadTbl] ADD  CONSTRAINT [DF_Transaction_HeadTbl_ShiftID]  DEFAULT ((0)) FOR [ShiftID]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPane1', @value=N'[0E232FF0-B466-11cf-A24F-00AA00A3EFFF, 1.00]
Begin DesignProperties = 
   Begin PaneConfigurations = 
      Begin PaneConfiguration = 0
         NumPanes = 4
         Configuration = "(H (1[42] 4[23] 2[5] 3) )"
      End
      Begin PaneConfiguration = 1
         NumPanes = 3
         Configuration = "(H (1 [50] 4 [25] 3))"
      End
      Begin PaneConfiguration = 2
         NumPanes = 3
         Configuration = "(H (1 [50] 2 [25] 3))"
      End
      Begin PaneConfiguration = 3
         NumPanes = 3
         Configuration = "(H (4 [30] 2 [40] 3))"
      End
      Begin PaneConfiguration = 4
         NumPanes = 2
         Configuration = "(H (1 [56] 3))"
      End
      Begin PaneConfiguration = 5
         NumPanes = 2
         Configuration = "(H (2 [66] 3))"
      End
      Begin PaneConfiguration = 6
         NumPanes = 2
         Configuration = "(H (4 [50] 3))"
      End
      Begin PaneConfiguration = 7
         NumPanes = 1
         Configuration = "(V (3))"
      End
      Begin PaneConfiguration = 8
         NumPanes = 3
         Configuration = "(H (1[56] 4[18] 2) )"
      End
      Begin PaneConfiguration = 9
         NumPanes = 2
         Configuration = "(H (1 [75] 4))"
      End
      Begin PaneConfiguration = 10
         NumPanes = 2
         Configuration = "(H (1[66] 2) )"
      End
      Begin PaneConfiguration = 11
         NumPanes = 2
         Configuration = "(H (4 [60] 2))"
      End
      Begin PaneConfiguration = 12
         NumPanes = 1
         Configuration = "(H (1) )"
      End
      Begin PaneConfiguration = 13
         NumPanes = 1
         Configuration = "(V (4))"
      End
      Begin PaneConfiguration = 14
         NumPanes = 1
         Configuration = "(V (2))"
      End
      ActivePaneConfig = 0
   End
   Begin DiagramPane = 
      Begin Origin = 
         Top = 0
         Left = 0
      End
      Begin Tables = 
         Begin Table = "Transaction_DetailsTbl"
            Begin Extent = 
               Top = 6
               Left = 38
               Bottom = 310
               Right = 257
            End
            DisplayFlags = 280
            TopColumn = 56
         End
         Begin Table = "Recipe_ProductsTbl"
            Begin Extent = 
               Top = 6
               Left = 295
               Bottom = 309
               Right = 510
            End
            DisplayFlags = 280
            TopColumn = 0
         End
      End
   End
   Begin SQLPane = 
   End
   Begin DataPane = 
      Begin ParameterDefaults = ""
      End
      Begin ColumnWidths = 16
         Width = 284
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 2028
         Width = 1920
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1200
      End
   End
   Begin CriteriaPane = 
      Begin ColumnWidths = 11
         Column = 1440
         Alias = 900
         Table = 1176
         Output = 720
         Append = 1400
         NewValue = 1170
         SortType = 1356
         SortOrder = 1416
         GroupBy = 1350
         Filter = 1356
         Or = 1350
         Or = 1350
         Or = 1350
      End
   End
End
' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'ProductionDetails'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPaneCount', @value=1 , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'ProductionDetails'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPane1', @value=N'[0E232FF0-B466-11cf-A24F-00AA00A3EFFF, 1.00]
Begin DesignProperties = 
   Begin PaneConfigurations = 
      Begin PaneConfiguration = 0
         NumPanes = 4
         Configuration = "(H (1[31] 4[34] 2[9] 3) )"
      End
      Begin PaneConfiguration = 1
         NumPanes = 3
         Configuration = "(H (1 [50] 4 [25] 3))"
      End
      Begin PaneConfiguration = 2
         NumPanes = 3
         Configuration = "(H (1 [50] 2 [25] 3))"
      End
      Begin PaneConfiguration = 3
         NumPanes = 3
         Configuration = "(H (4 [30] 2 [40] 3))"
      End
      Begin PaneConfiguration = 4
         NumPanes = 2
         Configuration = "(H (1 [56] 3))"
      End
      Begin PaneConfiguration = 5
         NumPanes = 2
         Configuration = "(H (2 [66] 3))"
      End
      Begin PaneConfiguration = 6
         NumPanes = 2
         Configuration = "(H (4 [50] 3))"
      End
      Begin PaneConfiguration = 7
         NumPanes = 1
         Configuration = "(V (3))"
      End
      Begin PaneConfiguration = 8
         NumPanes = 3
         Configuration = "(H (1[56] 4[18] 2) )"
      End
      Begin PaneConfiguration = 9
         NumPanes = 2
         Configuration = "(H (1 [75] 4))"
      End
      Begin PaneConfiguration = 10
         NumPanes = 2
         Configuration = "(H (1[66] 2) )"
      End
      Begin PaneConfiguration = 11
         NumPanes = 2
         Configuration = "(H (4 [60] 2))"
      End
      Begin PaneConfiguration = 12
         NumPanes = 1
         Configuration = "(H (1) )"
      End
      Begin PaneConfiguration = 13
         NumPanes = 1
         Configuration = "(V (4))"
      End
      Begin PaneConfiguration = 14
         NumPanes = 1
         Configuration = "(V (2))"
      End
      ActivePaneConfig = 0
   End
   Begin DiagramPane = 
      Begin Origin = 
         Top = 0
         Left = 0
      End
      Begin Tables = 
         Begin Table = "Transaction_DetailsTbl"
            Begin Extent = 
               Top = 27
               Left = 393
               Bottom = 232
               Right = 612
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "Transaction_HeadTbl"
            Begin Extent = 
               Top = 16
               Left = 637
               Bottom = 228
               Right = 860
            End
            DisplayFlags = 280
            TopColumn = 17
         End
         Begin Table = "UsersTbl"
            Begin Extent = 
               Top = 33
               Left = 906
               Bottom = 163
               Right = 1082
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "Recipe_ProductsTbl"
            Begin Extent = 
               Top = 6
               Left = 38
               Bottom = 271
               Right = 253
            End
            DisplayFlags = 280
            TopColumn = 0
         End
      End
   End
   Begin SQLPane = 
   End
   Begin DataPane = 
      Begin ParameterDefaults = ""
      End
      Begin ColumnWidths = 18
         Width = 284
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
      End
   End
   Begin CriteriaPane = 
      Begin ColumnWidths = 12
         Column = 3720
         Alias = 900
         Table = 2730
' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'ProductionMaster'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPane2', @value=N'         Output = 720
         Append = 1400
         NewValue = 1170
         SortType = 1350
         SortOrder = 1410
         GroupBy = 1350
         Filter = 1350
         Or = 1350
         Or = 1350
         Or = 1350
      End
   End
End
' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'ProductionMaster'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPaneCount', @value=2 , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'ProductionMaster'
GO
