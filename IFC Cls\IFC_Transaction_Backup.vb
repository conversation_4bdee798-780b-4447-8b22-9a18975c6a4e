Public Class IFC_Transaction
    Dim Conn As New Conn_Cls
    'Dim ClsOnHand As New Cls_StockOnHand
    'Dim Clsprod As New Cls_Products
    'Dim ClsPatch As New Cls_Patch
    Dim ClsOnHand As New OnHand_Cls

    ' Enhanced structures for recipe processing
    Public Structure RecipeCalculationResult
        Public Success As Boolean
        Public TotalCost As Decimal
        Public IngredientDeductions As List(Of IngredientDeduction)
        Public ErrorMessage As String
        Public ProductionRequired As List(Of ProductionRequirement)
        Public ProcessingTime As TimeSpan
        Public RecursiveDepth As Integer
        Public TotalIngredientsProcessed As Integer
    End Structure

    Public Structure IngredientDeduction
        Public ProductId As Integer
        Public ProductCode As String
        Public ProductName As String
        Public CostCenterId As Integer
        Public CostCenterName As String
        Public RequiredQuantity As Decimal
        Public AvailableQuantity As Decimal
        Public DeductedQuantity As Decimal
        Public UnitCost As Decimal
        Public TotalCost As Decimal
        Public PatchSer As Integer
        Public PatchName As String
        Public IsProduced As Boolean
        Public RecipeLevel As Integer
        Public SourceCostCenters As List(Of CostCenterDeduction)
        Public IsRecursiveIngredient As Boolean
        Public ParentProductId As Integer
    End Structure

    Public Structure CostCenterDeduction
        Public CostCenterId As Integer
        Public CostCenterName As String
        Public DeductedQuantity As Decimal
        Public UnitCost As Decimal
        Public TotalCost As Decimal
        Public Priority As Integer
    End Structure

    Public Structure ProductionRequirement
        Public ProductId As Integer
        Public ProductCode As String
        Public ProductName As String
        Public RequiredQuantity As Decimal
        Public Recipe As List(Of IngredientDeduction)
        Public EstimatedCost As Decimal
        Public Priority As Integer
        Public RecursionLevel As Integer
        Public ParentProductIds As List(Of Integer)
    End Structure

    Public Structure CostCenterPriority
        Public CostCenterId As Integer
        Public CostCenterName As String
        Public Priority As Integer
        Public AvailableQuantity As Decimal
        Public AvgCost As Decimal
        Public IsDefault As Boolean
    End Structure
    Public Function LoadPosDataOnLine(Fromdate As DateTime, Todate As DateTime, Optional IsLoding As Boolean = False) As DataTable
        Dim SqlStr As String = ""
        Dim CompanyPOS_Id, OutLetPOS_Id, CostCenterPOS_Id, FoDates, ToDates, SqlPul As String
        CompanyPOS_Id = ""
        OutLetPOS_Id = ""
        CostCenterPOS_Id = ""
        SqlPul = ""
        If IsLoding Then SqlPul = " and plu=0 "
        Dim DtScm As New DataTable
        DtScm = Conn.SELECT_TXT(" select * from POSSetting ")
        'If DtScm.Rows.Count > 0 Then
        For R As Integer = 0 To DtScm.Rows.Count - 1


            If CompanyPOS_Id = "" Then
                CompanyPOS_Id = DtScm.Rows(R)("Company_IdPOS")
            Else
                CompanyPOS_Id = CompanyPOS_Id & "," & DtScm.Rows(R)("Company_IdPOS")
            End If


            If OutLetPOS_Id = "" Then
                OutLetPOS_Id = DtScm.Rows(R)("Brand_IdPOS")
            Else
                OutLetPOS_Id = OutLetPOS_Id & "," & DtScm.Rows(R)("Brand_IdPOS")
            End If


            If CostCenterPOS_Id = "" Then
                CostCenterPOS_Id = DtScm.Rows(R)("CostCenter_IdPOS")
            Else
                CostCenterPOS_Id = CostCenterPOS_Id & "," & DtScm.Rows(R)("CostCenter_IdPOS")
            End If
        Next

        FoDates = Format(Fromdate, "yyyy/MM/dd 00:00:00")
        ToDates = Format(Todate, "yyyy/MM/dd 23:59:59")
        If Conn.POS = "Matrix POS" Then
            'SqlStr = "  SELECT   mandant AS CompanyPOS_Id, outlet AS OutLetPOS_Id, center AS CostCenterPOS_Id, centername AS CostCenterPOS_Name, plu AS Product_Code, article AS Product_Name, SUM(Qty) AS Reciving_Q, "
            'SqlStr = SqlStr & "      price AS Sales_Price, SUM(Amount) AS CostTotalLine, SUM(discount) AS discount, SUM(0 * (Amount_10 + Amount_12)) AS taxamount, statistdate AS Transaction_Date_Create, payform AS MethodOfPayment_Id, "
            'SqlStr = SqlStr & "          payformname AS MethodOfPayment_Name, billnum AS Check_No"
            'SqlStr = SqlStr & "      FROM dbo.v_Orders"
            'SqlStr = SqlStr & "     GROUP BY outlet, statistdate, mandant, plu, center, centername, price, article, payform, payformname, billnum"
            'SqlStr = SqlStr & "     HAVING (mandant =" & CompanyPOS_Id & ") AND (outlet =" & OutLetPOS_Id & ") AND (center =" & CostCenterPOS_Id & ")"
            'SqlStr = SqlStr & "     ORDER BY CompanyPOS_Id, OutLetPOS_Id, Product_Code"

            SqlStr = " select * from v_ProcIFC "
            SqlStr = SqlStr & " where mandant in (" & CompanyPOS_Id & ") and outlet in (" & OutLetPOS_Id & ") and center in (" & CostCenterPOS_Id & ") "
            ' SqlStr = SqlStr & " and (statistdate >= CONVERT(DATETIME, '" & FoDates & "', 102)) AND (statistdate <= CONVERT(DATETIME,'" & ToDates & "', 102))"
            SqlStr = SqlStr & " And IsIFCDone=0  "
            SqlStr = SqlStr & SqlPul & "  ORDER BY mandant, outlet, center,plu "
        End If


        If Conn.POS = "Smart POS" Then
            CheckExistV_ProcIFC()
            SqlStr = " select * from v_ProcIFC "
            SqlStr = SqlStr & " where mandant in (" & CompanyPOS_Id & ") and outlet in (" & OutLetPOS_Id & ") and center in (" & CostCenterPOS_Id & ") "
            '     SqlStr = SqlStr & " and (statistdate >= CONVERT(DATETIME, '" & FoDates & "', 102)) AND (statistdate <= CONVERT(DATETIME,'" & ToDates & "', 102))"
            SqlStr = SqlStr & " And IsIFCDone=0  "
            SqlStr = SqlStr & SqlPul & "  ORDER BY mandant, outlet, center,plu "
        End If

        Dim Dt As New DataTable
        Dt.Clear()
        Dt = Conn.SELECT_TXTPOS(SqlStr)

        Return Dt

    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    Public Sub UpdateCHDIDOnline(ChDID As Integer)
        Conn.EXECUT_TxtPOS("Update Check_Details Set IsIFCDone=1 Where ChDID=" & ChDID & "")
    End Sub
    Public Function LoadPosData(Fromdate As DateTime, Todate As DateTime, CostCenter_IdPOS As Integer, Optional IsLoding As Boolean = False) As DataTable
        Dim SqlStr As String = ""
        Dim CompanyPOS_Id, OutLetPOS_Id, CostCenterPOS_Id, FoDates, ToDates, SqlPul As String
        CompanyPOS_Id = ""
        OutLetPOS_Id = ""
        CostCenterPOS_Id = ""
        SqlPul = ""
        If IsLoding Then SqlPul = " and plu=0 "
        Dim DtScm As New DataTable
        DtScm = Conn.SELECT_TXT(" select * from POSSetting Where CostCenter_IdPOS=" & CostCenter_IdPOS & "")
        'If DtScm.Rows.Count > 0 Then
        For R As Integer = 0 To DtScm.Rows.Count - 1


            If CompanyPOS_Id = "" Then
                CompanyPOS_Id = DtScm.Rows(R)("Company_IdPOS")
            Else
                CompanyPOS_Id = CompanyPOS_Id & "," & DtScm.Rows(R)("Company_IdPOS")
            End If


            If OutLetPOS_Id = "" Then
                OutLetPOS_Id = DtScm.Rows(R)("Brand_IdPOS")
            Else
                OutLetPOS_Id = OutLetPOS_Id & ", " & DtScm.Rows(R)("Brand_IdPOS")
            End If


            If CostCenterPOS_Id = "" Then
                CostCenterPOS_Id = DtScm.Rows(R)("CostCenter_IdPOS")
            Else
                CostCenterPOS_Id = CostCenterPOS_Id & "," & DtScm.Rows(R)("CostCenter_IdPOS")
            End If
        Next

        FoDates = Format(Fromdate, "yyyy/MM/dd 00:00:00")
        ToDates = Format(Todate, "yyyy/MM/dd 23:59:59")
        If Conn.POS = "Matrix POS" Then
            'SqlStr = "  SELECT   mandant AS CompanyPOS_Id, outlet AS OutLetPOS_Id, center AS CostCenterPOS_Id, centername AS CostCenterPOS_Name, plu AS Product_Code, article AS Product_Name, SUM(Qty) AS Reciving_Q, "
            'SqlStr = SqlStr & "      price AS Sales_Price, SUM(Amount) AS CostTotalLine, SUM(discount) AS discount, SUM(0 * (Amount_10 + Amount_12)) AS taxamount, statistdate AS Transaction_Date_Create, payform AS MethodOfPayment_Id, "
            'SqlStr = SqlStr & "          payformname AS MethodOfPayment_Name, billnum AS Check_No"
            'SqlStr = SqlStr & "      FROM dbo.v_Orders"
            'SqlStr = SqlStr & "     GROUP BY outlet, statistdate, mandant, plu, center, centername, price, article, payform, payformname, billnum"
            'SqlStr = SqlStr & "     HAVING (mandant =" & CompanyPOS_Id & ") AND (outlet =" & OutLetPOS_Id & ") AND (center =" & CostCenterPOS_Id & ")"
            'SqlStr = SqlStr & "     ORDER BY CompanyPOS_Id, OutLetPOS_Id, Product_Code"

            SqlStr = " select * from v_ProcIFC "
            SqlStr = SqlStr & " where mandant in (" & CompanyPOS_Id & ") and outlet in (" & OutLetPOS_Id & ") and center in (" & CostCenterPOS_Id & ") "
            SqlStr = SqlStr & " and (statistdate >= CONVERT(DATETIME, '" & FoDates & "', 102)) AND (statistdate <= CONVERT(DATETIME,'" & ToDates & "', 102))"
            SqlStr = SqlStr & SqlPul & "  ORDER BY mandant, outlet, center,plu "
        End If


        If Conn.POS = "Smart POS" Then
            CheckExistV_ProcIFC()
            SqlStr = " select * from v_ProcIFC "
            SqlStr = SqlStr & " where mandant in (" & CompanyPOS_Id & ") and outlet in (" & OutLetPOS_Id & ") and center in (" & CostCenterPOS_Id & ") "
            SqlStr = SqlStr & " and (statistdate >= CONVERT(DATETIME, '" & FoDates & "', 102)) AND (statistdate <= CONVERT(DATETIME,'" & ToDates & "', 102))"
            SqlStr = SqlStr & SqlPul & "  ORDER BY mandant, outlet, center,plu "
        End If

        Dim Dt As New DataTable
        Dt.Clear()
        Dt = Conn.SELECT_TXTPOS(SqlStr)

        Return Dt

    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function
    Public Sub CheckExistV_ProcIFC()
        Dim Dt As New DataTable
        Dt.Clear()
        Dt = Conn.SELECT_TXTPOS("SELECT * FROM sys.objects WHERE object_id=OBJECT_ID('v_ProcIFC') ")
        If Dt.Rows.Count > 0 Then Return
        Dim Sql As String = ""

        If Conn.POS = "Smart POS" Then
            '            Sql = "CREATE VIEW  v_ProcIFC
            'AS
            'SELECT CCGCode AS mandant, CCCode AS outlet, FloorID AS center, FloorName AS centername, ArtCode AS plu, ArtName AS article, SUM(NetQty) AS Qty, AVG(UnitPrice) AS price, SUM(NetPrice) AS Amount, SUM(DeducAmount) AS discount,
            '                  SUM(ChkTaxesAmount_1 + ChkTaxesAmount_2) AS taxamount, SysDate AS statistdate, OTCode AS payform, OTName AS payformname, SUM(ChkPrtID) AS billnum
            'FROM     dbo.vOrders
            'GROUP BY CCGCode, CCCode, FloorID, FloorName, ArtCode, ArtName, SysDate, OTCode, OTName"


            Sql = "CREATE VIEW [dbo].[v_ProcIFC]
AS
SELECT        CCGCode AS mandant, CCCode AS outlet, FloorID AS center, FloorName AS centername, ArtCode AS plu, ArtName AS article, SUM(NetQty) AS Qty, AVG(UnitPrice) AS price, SUM(NetPrice) AS Amount,
                         SUM(DeducAmount) AS discount, SUM(ChkTaxesAmount_1 + ChkTaxesAmount_2) AS taxamount, SysDate AS statistdate, SUM(CASE (dbo.vOrders.IsIFCDone) WHEN 1 THEN 1 ELSE 0 END) AS IsIFCDone,
                         0 AS ChDID
FROM            dbo.vOrders
GROUP BY CCGCode, CCCode, FloorID, FloorName, ArtCode, ArtName, SysDate
"

            Conn.EXECUT_TxtPOS(Sql)
        End If

    End Sub
    Public Function GetLasttrans_date(Dte As DateTime) As DataTable
        Dim Dt As New DataTable
        Dt.Clear()
        Dim DteFirts, DteLast As String
        DteFirts = Dte.ToString("yyyy/MM/dd 00:00:00")
        DteLast = Dte.ToString("yyyy/MM/dd 23:59:59")
        Dt = Conn.SELECT_TXT("select * from Sales_POS where (Transaction_Date_Create >= CONVERT(DATETIME, '" & DteFirts & "', 102)) AND (Transaction_Date_Create <= CONVERT(DATETIME,'" & DteLast & "', 102)) order by Transaction_Date_Create desc")
        Return Dt
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    Public Function SalePosLoadInDGVSCM() As DataTable
        Dim Dt As New DataTable
        Dt.Clear()
        Dt = Conn.SELECT_TXT("select * FROM     Sales_POS where Ser=0")
        Return Dt
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    Public Function GetSettingPOS(CompanyId As Integer, outLetId As Integer, CostID As Integer) As DataTable
        Dim Dt As New DataTable
        Dt.Clear()
        Dt = Conn.SELECT_TXT("select * FROM      POSSetting where Company_IdPOS='" & CompanyId & "' and Brand_IdPOS='" & outLetId & "' and CostCenter_IdPOS='" & CostID & "'")
        Return Dt
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    Public Function GetProductData(ProductCode As String) As DataTable
        Dim Dt As New DataTable
        Dt.Clear()
        If ProductCode = "11203" Then
            Dim xxx As String = ""
        End If
        Dt = Conn.SELECT_TXT("select * FROM      ProductsTbl where Product_Code='" & ProductCode & "' and (IsSales = 1)")
        Return Dt
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function
    ', CostCenter_Id As Integer
    Public Function GetSOHData(Product_Id As Integer) As DataTable
        Dim Dt As New DataTable
        Dt.Clear()
        Dt = Conn.SELECT_TXT("select * FROM      StockOnHandTbl where Product_Id=" & Product_Id & " order by CostCenter_Id ")
        Return Dt
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function
    Public Function GetSOHDataGetting(Product_Id As Integer, Sql As String) As DataTable
        Dim Dt As New DataTable
        Dt.Clear()
        'order by CostCenter_Id
        Dt = Conn.SELECT_TXT("select * FROM      StockOnHandTbl where Product_Id=" & Product_Id & " " + Sql)
        Return Dt
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function
    Public Function GetExpiredData(Product_Id As Integer, CostCenter_Id As Integer) As DataTable
        Dim Dt As New DataTable
        Dt.Clear()
        Dt = Conn.SELECT_TXT("select * FROM  Patches where Product_Id=" & Product_Id & " and CostCenter_Id=" & CostCenter_Id & "  and CloseOpen=0 and usd=0 order by Exp_Date ")
        Return Dt
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    Public Function GetCostCenterData(CostCenter_Id As Integer) As DataTable
        Dim Dt As New DataTable
        Dt.Clear()
        '   Dt = Conn.SELECT_TXT("select * FROM       CostCenterTbl where Store_id=" & CostCenter_Id & "  ")
        Dt = Conn.SELECT_TXT("select * from CostCenterLinkPOS where Ser=" & CostCenter_Id & " ")
        Return Dt
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function
    Public Function GetCostCenterDataTrue(CostCenter_Id As Integer) As DataTable
        Dim Dt As New DataTable
        Dt.Clear()
        Dt = Conn.SELECT_TXT("select * FROM       CostCenterTbl where CostCenter_Id=" & CostCenter_Id & "  ")
        Return Dt
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function
    Public Sub UpdateUsePatch(Patch_Ser As Integer, Product_Id As Integer, CostCenter_Id As Integer)
        Conn.EXECUT_Txt("Update Patches set usd=1 where Patch_Ser=" & Patch_Ser & " and Product_Id=" & Product_Id & " and CostCenter_Id=" & CostCenter_Id & "")
    End Sub

    Public Function GetSOHData(Product_Id As Integer, CostCenter_Id As Integer) As DataTable
        Dim Dt As New DataTable
        Dt.Clear()
        Dt = Conn.SELECT_TXT("select * FROM      StockOnHandTbl where Product_Id=" & Product_Id & " and CostCenter_Id=" & CostCenter_Id & " ")
        Return Dt
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    Public Function GetTransCodeSales() As Integer
        Dim SqlStr As String
        Dim Id As Integer = 0
        Dim Dt As New DataTable
        SqlStr = "select ISNULL(MAX(Transaction_Code)+1,1) from Transaction_HeadTbl where Transaction_Id=9 "
        Dt = Conn.SELECT_TXT(SqlStr)
        Id = Dt.Rows(0)(0)
        Return Id
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    Public Function GetTransCodeProduction() As Integer
        Dim SqlStr As String
        Dim Id As Integer = 0
        Dim Dt As New DataTable
        SqlStr = "select ISNULL(MAX(Transaction_Code)+1,1) from Transaction_HeadTbl where Transaction_Id=7 "
        Dt = Conn.SELECT_TXT(SqlStr)
        Id = Dt.Rows(0)(0)
        Return Id
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    Public Sub Head_Insert(Transaction_Code As String, CostCenter_Id As Integer, Suppliers_Id As Integer, CostCenter_Supplier As String, Remarks As String, Invoice_No As String, Amount_Bill As Double, Tax_Bill As Double, Total_Amount As Double, Transaction_Save As Boolean, Transaction_Submit As Boolean, User_Id As Integer, Utholization_Id As Integer, Transaction_PaidAmount As Double, Transaction_NetAmount As Double, Authulized As Boolean, Transaction_Date As DateTime, Transaction_Date_Submit As DateTime, Transaction_Patch As String, User_Id_Submit As Integer, Invoice_NoReciving As String, TransId As Integer)


        Dim SqlStr, SubmitDate, transDate As String
        'SubmitDate = Format(Transaction_Date_Submit, "yyyy-MM-dd")
        'transDate = Format(Transaction_Date, "yyyy-MM-dd")
        transDate = Transaction_Date.ToString("yyyy-MM-dd HH:mm:ss")
        SubmitDate = Transaction_Date_Submit.ToString("yyyy-MM-dd HH:mm:ss")

        If Transaction_Patch = "" Then

            ',User_IdAccept
            SqlStr = " INSERT INTO Transaction_HeadTbl (Transaction_Code ,CostCenter_Id,Suppliers_Id,CostCenter_Supplier,Remarks,Invoice_No,Amount_Bill,Tax_Bill,Total_Amount,Transaction_Save,Transaction_Submit,User_Id,Utholization_Id,Transaction_PaidAmount,Transaction_NetAmount,Authulized,Transaction_Id,Transaction_Date,Transaction_Date_Submit,Accept_Reciving,User_Id_Submit,Invoice_NoReciving) VALUES"
            SqlStr = SqlStr & "('" & Transaction_Code & "' ," & CostCenter_Id & "," & Suppliers_Id & ",'" & CostCenter_Supplier & "','" & Remarks & "','" & Invoice_No & "','" & Amount_Bill & "','" & Tax_Bill & "','" & Total_Amount & "','" & Transaction_Save & "','" & Transaction_Submit & "'," & User_Id & "," & Utholization_Id & ",'" & Transaction_PaidAmount & "','" & Transaction_NetAmount & "','" & Authulized & "'," & TransId & ",'" & transDate & "','" & SubmitDate & "',0," & User_Id_Submit & ",'" & Invoice_NoReciving & "')"
        Else
            SqlStr = " INSERT INTO Transaction_HeadTbl (Transaction_Code ,CostCenter_Id,Suppliers_Id,CostCenter_Supplier,Remarks,Invoice_No,Amount_Bill,Tax_Bill,Total_Amount,Transaction_Save,Transaction_Submit,User_Id,Utholization_Id,Transaction_PaidAmount,Transaction_NetAmount,Authulized,Transaction_Id,Transaction_Date,Transaction_Date_Submit,Transaction_Patch,Accept_Reciving,User_Id_Submit,Invoice_NoReciving) VALUES"
            SqlStr = SqlStr & " ('" & Transaction_Code & "' ," & CostCenter_Id & "," & Suppliers_Id & ",'" & CostCenter_Supplier & "','" & Remarks & "','" & Invoice_No & "','" & Amount_Bill & "','" & Tax_Bill & "','" & Total_Amount & "','" & Transaction_Save & "','" & Transaction_Submit & "'," & User_Id & "," & Utholization_Id & ",'" & Transaction_PaidAmount & "','" & Transaction_NetAmount & "','" & Authulized & "'," & TransId & ",'" & transDate & "','" & SubmitDate & "','" & Transaction_Patch & "',0," & User_Id_Submit & ",'" & Invoice_NoReciving & "')"

        End If
        Conn.EXECUT_Txt(SqlStr)
        ','" & Transaction_Patch & "'
        Dim Dt As New DataTable
        Dt = ShowHeadWithConditions_(" and Transaction_Code='" & Transaction_Code & "'", TransId)
        If Dt.Rows.Count > 0 Then
            Transaction_Patch = Dt.Rows(0)("Transaction_Patch").ToString()
        End If

        Dim SqlStrHestory As String
        SqlStrHestory = " INSERT INTO Transaction_Head_HistoryTbl (Transaction_Code ,CostCenter_Id,Suppliers_Id,CostCenter_Supplier,Remarks,Invoice_No,Amount_Bill,Tax_Bill,Total_Amount,Transaction_Save,Transaction_Submit,User_Id,Utholization_Id,Transaction_PaidAmount,Transaction_NetAmount,Authulized,Transaction_Id,Transaction_Date,Transaction_Date_Submit,Transaction_Patch,Accept_Reciving,User_Id_Submit,Invoice_NoReciving) VALUES"
        SqlStrHestory = SqlStrHestory & " ('" & Transaction_Code & "' ," & CostCenter_Id & "," & Suppliers_Id & ",'" & CostCenter_Supplier & "','" & Remarks & "','" & Invoice_No & "','" & Amount_Bill & "','" & Tax_Bill & "','" & Total_Amount & "','" & Transaction_Save & "','" & Transaction_Submit & "'," & User_Id & "," & Utholization_Id & ",'" & Transaction_PaidAmount & "','" & Transaction_NetAmount & "','" & Authulized & "'," & TransId & ",'" & transDate & "','" & SubmitDate & "','" & Transaction_Patch & "',0," & User_Id_Submit & ",'" & Invoice_NoReciving & "')"

        Conn.EXECUT_Txt(SqlStrHestory)
    End Sub
    Public Function ShowHeadWithConditions_(Conditions_ As String, TransId As Integer)
        Dim DT As New DataTable
        DT.Clear()
        Dim SqlStr As String
        SqlStr = "SELECT   Transaction_Code, CostCenter_Id, Suppliers_Id, CostCenter_Supplier, Remarks, Invoice_No, Amount_Bill, Tax_Bill, Total_Amount, Transaction_Save, Transaction_Submit, User_Id, Utholization_Id, "
        SqlStr = SqlStr & " Transaction_PaidAmount, Transaction_NetAmount, Authulized, Ser, Transaction_Id, Transaction_Date, Transaction_Date_Submit, Transaction_Patch, Accept_Reciving, User_Id_Submit, User_IdAccept,Invoice_NoReciving "
        SqlStr = SqlStr & " FROM Transaction_HeadTbl where Ser is not null  AND (Transaction_Id =" & TransId & ") " & Conditions_ & ""

        DT = Conn.SELECT_TXT(SqlStr)
        Return DT

    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    Public Function GetPatchHead(Transaction_Code As Integer, Transaction_Id As Integer) As String
        Dim SqlStr As String
        Dim Patches As String = ""
        Dim Dt As New DataTable
        SqlStr = "select * from Transaction_HeadTbl where Transaction_Code=" & Transaction_Code & " and Transaction_Id=" & Transaction_Id & " "
        Dt = Conn.SELECT_TXT(SqlStr)
        If Dt.Rows.Count > 0 Then
            Patches = Dt.Rows(0)("Transaction_Patch").ToString()
        End If

        Return Patches
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    Public Sub UpdateHeadAmount(Transaction_Code As Integer, Transaction_Id As Integer, Amount As Decimal)

        Conn.EXECUT_Txt("Update Transaction_HeadTbl set Amount_Bill='" & Amount & "',Total_Amount='" & Amount & "',Transaction_NetAmount='" & Amount & "'   where Transaction_Code=" & Transaction_Code & " and Transaction_Id=" & Transaction_Id & "")
    End Sub

    Public Sub UpdateSOH(Product_Id As Integer, CostCenter_Id As Integer, Quantity As Decimal, BaseUnit As Decimal)


        Conn.EXECUT_Txt("Update  StockOnHandTbl set  Quntity='" & Quantity & "',QuntityBase='" & BaseUnit & "' where Product_Id=" & Product_Id & " and CostCenter_Id=" & CostCenter_Id & " ")
    End Sub

    Public Sub UpdatePatchExpired(PatchSer As Integer, Quanitity As Decimal)
        Dim PatchQuantity, Deff As Decimal
        Dim CloseOpen As Boolean
        Dim Dt As New DataTable
        Dt.Clear()
        Dt = Conn.SELECT_TXT("select * from Patches where Patch_Ser=" & PatchSer & "")

        Quanitity = Math.Abs(Quanitity)
        If Dt.Rows.Count > 0 Then
            PatchQuantity = Convert.ToDecimal(Dt.Rows(0)("NetQ_Qsetup_CurrentQ"))
            Deff = Quanitity - PatchQuantity
            If Quanitity >= PatchQuantity Then
                CloseOpen = True
                Conn.EXECUT_Txt("Update Patches set NetQ_Qsetup_CurrentQ='" & Deff & "',usd=0,CloseOpen='" & CloseOpen & "'  where Patch_Ser=" & PatchSer & "")
            Else

                Conn.EXECUT_Txt("Update Patches set NetQ_Qsetup_CurrentQ='" & Deff & "',usd=0,CloseOpen='" & CloseOpen & "'  where Patch_Ser=" & PatchSer & "")
            End If


        End If
    End Sub

    Public Sub UpdatePatcheUseFalse(PatchSer As String)

        Conn.EXECUT_Txt("Update Patches set usd=0 where Patch_Ser in (" & PatchSer & ")")
    End Sub
    Public Function ShowWithProduct_IdAll(Product_Id As Integer)
        Dim sqlString As String
        sqlString = "SELECT * FROM    Recipe_ProductsTbl "
        sqlString = sqlString & " where Product_Id=" & Product_Id & ""
        Dim DT As New DataTable
        DT.Clear()
        DT = Conn.SELECT_TXT(sqlString)
        Return DT

    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    'Public Function ShowProductIdExpireDate_(Product_Id As Integer, CostCenter_Id As Integer)
    '    Dim DT As New DataTable
    '    DT.Clear()

    '    Dim SqlStr As String

    '    SqlStr = " SELECT       Patch_Ser, Patch_Name, Product_Code, Product_Name, CostCenter_Id, CostCenter_Name, Unt_Id, Unt_Name, Unt_GroupId, Unt_Q, Current_Unt_Id, Current_Unt_Name, Current_Unt_Q, "
    '    SqlStr = SqlStr & "  NetQ_Qsetup_CurrentQ, Prud_Date, Exp_Date, CloseOpen, usd, Product_Id "
    '    SqlStr = SqlStr & "   FROM Patches"

    '    SqlStr = SqlStr & " where Product_Id=" & Product_Id & " and CostCenter_Id=" & CostCenter_Id & " and CloseOpen=0  order by Exp_Date "
    '    DT = Conn.SELECT_TXT(SqlStr)
    '    Return DT

    'End Function
    Public Function ShowUsdFalse_(Product_Id As Integer, CostCenter_Id As Integer)
        Dim DT As New DataTable
        DT.Clear()

        Dim SqlStr As String

        SqlStr = " SELECT       Patch_Ser, Patch_Name, Product_Code, Product_Name, CostCenter_Id, CostCenter_Name, Unt_Id, Unt_Name, Unt_GroupId, Unt_Q, Current_Unt_Id, Current_Unt_Name, Current_Unt_Q, "
        SqlStr = SqlStr & "  NetQ_Qsetup_CurrentQ, Prud_Date, Exp_Date, CloseOpen, usd, Product_Id"
        SqlStr = SqlStr & "   FROM Patches"

        SqlStr = SqlStr & " where Product_Id=" & Product_Id & " and  CostCenter_Id=" & CostCenter_Id & " and CloseOpen=0 and usd=0 order by Exp_Date "
        DT = Conn.SELECT_TXT(SqlStr)
        Return DT
        'Patch_Name='" & Patch_Name & "' and
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function
    Public Sub UpdatePatchUse_True(Patch_Ser As Integer)
        Conn.EXECUT_Txt("UPDATE Patches  SET usd=1 where  Patch_Ser=" & Patch_Ser & "")
    End Sub
    Public Function ShowSerOpen_(Patch_Ser As Integer, CostCenter_Id As Integer)
        Dim DT As New DataTable
        DT.Clear()
        'and usd=0
        Dim SqlStr As String

        SqlStr = " SELECT       Patch_Ser, Patch_Name, Product_Code, Product_Name, CostCenter_Id, CostCenter_Name, Unt_Id, Unt_Name, Unt_GroupId, Unt_Q, Current_Unt_Id, Current_Unt_Name, Current_Unt_Q, "
        SqlStr = SqlStr & "  NetQ_Qsetup_CurrentQ, Prud_Date, Exp_Date, CloseOpen, usd, Product_Id"
        SqlStr = SqlStr & "   FROM Patches "

        SqlStr = SqlStr & " where Patch_Ser=" & Patch_Ser & "  and CloseOpen=0  and CostCenter_Id=" & CostCenter_Id & ""
        DT = Conn.SELECT_TXT(SqlStr)
        Return DT
        'and usd=0
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function
    Public Function CheckSumQPatch(Product_Id As Integer, CostCenter_Id As Integer)
        Dim DT As New DataTable
        DT.Clear()

        Dim SqlStr As String

        SqlStr = " SELECT        CostCenter_Id, Product_Id, SUM(NetQ_Qsetup_CurrentQ) AS PatchQ"
        SqlStr = SqlStr & " FROM  Patches"
        SqlStr = SqlStr & " GROUP BY CostCenter_Id, Product_Id HAVING         Product_Id=" & Product_Id & " and CostCenter_Id=" & CostCenter_Id & ""
        DT = Conn.SELECT_TXT(SqlStr)
        Return DT

    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function
    Public Function CheckValiDate(CostCenter_Id As Integer, CurrDate As DateTime, CostCenterName As String) As Boolean
        Dim Dt As New DataTable
        Dim Valuereturn As Boolean = False
        Dim MyFromDate, MyToDate As DateTime
        Dim SerProd As Integer


        Dim R As Integer = 2

        For T As Integer = 1 To R
            If T = 1 Then
                Dt.Clear()
                Dt = ShowWithCostCenterIdFilter_(CostCenter_Id, CurrDate)
            Else
                Dt.Clear()
                Dt = ShowWithCostCenterIdFilter_(CostCenter_Id, CurrDate)
            End If
        Next


        If Dt.Rows.Count = 0 Then
            MessageBox.Show("Please Go To Stores For Define Period First For This Cost Center : " & CostCenterName, "Error Store Period", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return Valuereturn
        End If

        SerProd = Val(Dt.Rows(0)("ser"))
        MyFromDate = Convert.ToDateTime(Dt.Rows(0)("From_Period"))
        MyToDate = Convert.ToDateTime(Dt.Rows(0)("To_Priod"))

        Dt.Clear()
        Dt = ShowPeriodCloseWithFilter_(SerProd)

        If Dt.Rows.Count > 0 Then
            MyFromDate = Convert.ToDateTime(Dt.Rows(0)("per_to"))

        End If

        MyFromDate = Format(MyFromDate, "yyyy-MM-dd HH:mm:ss")
        MyToDate = Format(MyToDate, "yyyy-MM-dd HH:mm:ss")
        CurrDate = Format(CurrDate, "yyyy-MM-dd HH:mm:ss")

        If (CurrDate < MyFromDate Or CurrDate > MyToDate) Then
            MsgBox("Sorry Your Date Is Out Of Range Current Period With The Cost Center: " & CostCenterName)
            Return Valuereturn
        End If

        Valuereturn = True
        Return Valuereturn
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    Public Function GetQuantityNow(Product_Id As Integer, CostCenter_Id As Integer, Product_Code As String, Quntity As Decimal) As Decimal
        Dim Dt As New DataTable
        Dim Q As Decimal = 0
        Dt.Clear()
        'sum(Quntity) as
        Dt = Conn.SELECT_TXT("select  Quntity from StockOnHandTbl_POS where Product_Id=" & Product_Id & "  and CostCenter_Id=" & CostCenter_Id & "")
        'Quntity = Quntity * -1
        If Dt.Rows.Count = 0 Then

            Conn.EXECUT_Txt("Insert Into StockOnHandTbl_POS (Product_Id,  Product_Code, CostCenter_Id, Quntity) Values  (" & Product_Id & ",'" & Product_Code & "', " & CostCenter_Id & ",'" & Quntity & "')")

        Else
            Q = CDec(Dt.Rows(0)(0))

            Q = Q + Quntity
            Conn.EXECUT_Txt("update StockOnHandTbl_POS set Quntity='" & Q & "' where Product_Id=" & Product_Id & "  and CostCenter_Id=" & CostCenter_Id & "")
            Q = Q - Quntity
        End If

        Return Q
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function
    Public Sub DeleteQuantityNow()
        Conn.EXECUT_Txt("Truncate Table StockOnHandTbl_POS")
    End Sub
    Public Function ShowWithCostCenterIdFilterOld_(CostCenter_Id As Integer)
        Dim DT As New DataTable
        Dim Year_Num As Integer
        Year_Num = Val(Date.Now.Year)
        DT.Clear()
        'and Year_Num=" & Year_Num & "
        DT = Conn.SELECT_TXT("SELECT ser, Store_id, Store_Name, Month_No, From_Period, To_Priod, Year_Num, Period_Numm, prod, CostCenter_Id, CostCenter_Name FROM Period_Store where CostCenter_Id=" & CostCenter_Id & " and  (prod = 0)  ORDER BY Year_Num, Period_Numm ")
        Return DT

    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function
    Public Function ShowWithCostCenterIdFilter_(CostCenter_Id As Integer, Startdate As DateTime)
        Dim DT As New DataTable
        Dim Year_Num, Period_Numm As Integer
        Year_Num = Val(Date.Now.Year)
        ', EndDate As DateTime
        DT.Clear()
        Dim SQL As String = ""
        Dim FoDates, ToDates As String
        FoDates = Format(Startdate, "yyyy-MM/dd 00:00:00")
        ToDates = Format(Startdate, "yyyy-MM/dd 23:59:59")
        Period_Numm = Startdate.Month
        Year_Num = Startdate.Year
        'Dim Year_Num As Integer
        '  SQL = "  and (From_Period >= CONVERT(DATETIME, '" & FoDates & "', 102)) AND (From_Period <= CONVERT(DATETIME,'" & ToDates & "', 102))  "
        'and Year_Num=" & Year_Num & "
        SQL = " And Period_Numm=" & Period_Numm & "  and Year_Num=" & Year_Num & "" 'and  (prod = 0)
        DT = Conn.SELECT_TXT("SELECT ser, Store_id, Store_Name, Month_No, From_Period, To_Priod, Year_Num, Period_Numm, prod, CostCenter_Id, CostCenter_Name FROM Period_Store where CostCenter_Id=" & CostCenter_Id & "  " & SQL & " ORDER BY Year_Num, Period_Numm ")
        Return DT

    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function
    Public Function ShowPeriodCloseWithFilter_(per_ser As Integer)
        Dim DT As New DataTable
        DT.Clear()
        Dim SqlStr As String

        SqlStr = "SELECT         ser, per_no, per_frm, per_to, Product_Code, Product_Name, itm_opn, itm_cls, clos_dat, clos_tim, per_ser, CostCenter_Id, cls_opn, nam, Product_Id"
        SqlStr = SqlStr & " FROM            Period_Close  where per_ser=" & per_ser & " and cls_opn=1 order by ser desc"
        DT = Conn.SELECT_TXT(SqlStr)
        Return DT

    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    Public Sub Details_Save(Transaction_Code As String, Product_Id As Integer, Product_Code As String, Product_Name As String, Order_Q As Double, Reciving_Q As Double, Invoice_Q As Double, Return_Q As Double, Cost_Product As Double, CostTotalLine As Double, CostCenter_Id_Frm As Integer, CostCenter_Name_Frm As String, CostCenter_Id_To As Integer, CostCenter_Name_To As String, Suppliers_Id_Frm As Integer, Suppliers_Name_Frm As String, Suppliers_Id_To As Integer, Suppliers_Name_To As String, Supplier_Frm As Boolean, Supplier_To As Boolean, Patch_Ser As Integer, Patch_Name As String, IsExpire As Boolean, Unt_Id As Integer, Unt_Name As String, Unt_GroupId As Integer, Unt_Q As Double, Current_Unt_Id As Integer, Current_Unt_Name As String, Current_Unt_Q As Double, NetQ_Qsetup_CurrentQ As Double, Transaction_Date_Create As DateTime, Transaction_Save As Boolean, Transaction_Submit As Boolean, Transaction_Cancel As Boolean, Del As Boolean, Transaction_Patch As String, Authulized As Boolean, Gard As DateTime, IsProductions As Boolean, AvCost As Double, Optional Required_Quantity As Double = 0, Optional UsedQuantity As Double = 0, Optional Rid As Integer = 0)

        Dim Dt As New DataTable
        Dim FieldPro, Prapro As String

        If Rid = 0 Then
            FieldPro = ""
            Prapro = ""
        Else
            FieldPro = ",Product_Id_PRO"
            Prapro = "," & Rid
        End If
        Dim Open_Q, Close_Q, CollectAvCost, Open_QB, Close_QBase, Cost_ProductPerUnit, NetQ_Qsetup_CurrentQBase, Item_Unit As Double
        Dim soh As Double = 0
        Dim SqlStrDetails, transCreate, GardDate As String
        Dim SqlStrHistory As String
        'transCreate = Format(Transaction_Date_Create, "yyyy-MM-dd")
        If Authulized = True Then
            transCreate = Transaction_Date_Create.ToString("yyyy-MM-dd HH:mm:ss")
            GardDate = Gard.ToString("yyyy-MM-dd HH:mm:ss")

            soh = ClsOnHand.Save_Stock(Product_Id, Product_Name, Product_Code, CostCenter_Id_To, NetQ_Qsetup_CurrentQ, True, soh)

            Dt.Clear()
            Dt = ClsOnHand.Show_StockWithParam(Product_Id, CostCenter_Id_To)

            If Dt.Rows.Count = 0 Then

                Open_QB = 0

            Else

                Open_QB = Convert.ToDouble(Dt.Rows(0)("QuntityBase"))
            End If

            Item_Unit = Unt_Q
            NetQ_Qsetup_CurrentQBase = Reciving_Q * Current_Unt_Q


            Open_Q = NetQ_Qsetup_CurrentQ

            Close_Q = soh


            If IsProductions Then
                Open_Q = Open_Q * -1
                '  Open_QB = Open_QB * -1
            End If
            Open_Q = Close_Q + Open_Q
            Close_QBase = Val(Open_QB) + Val(NetQ_Qsetup_CurrentQBase)
            'CollectAvCost = Val(Close_Q) * Val(Cost_Product)
            'AvCost = Val(AvCost) * Val(Open_Q)
            Cost_ProductPerUnit = Cost_Product

            ClsOnHand.BaseUniteSOH(Product_Id, Product_Code, CostCenter_Id_To, Close_QBase, Unt_Q)

            'AvCost += CollectAvCost
            'AvCost = Val(AvCost) / Val(Close_Q)
            '//////////////////////////////// TransFer (IN) ///////////////////////////////////////////////////////////////////
            SqlStrDetails = " INSERT INTO Transaction_DetailsTbl (Transaction_Code,Product_Id,Product_Code,Product_Name,Order_Q,Reciving_Q,Invoice_Q,Return_Q,Cost_Product,CostTotalLine,CostCenter_Id_Frm,CostCenter_Name_Frm,CostCenter_Id_To,CostCenter_Name_To,Suppliers_Id_Frm,Suppliers_Name_Frm,Suppliers_Id_To,Suppliers_Name_To,Supplier_Frm,Supplier_To,Patch_Ser,Patch_Name,IsExpire,Unt_Id,Unt_Name,Unt_GroupId,Unt_Q,Current_Unt_Id,Current_Unt_Name,Current_Unt_Q,NetQ_Qsetup_CurrentQ,Transaction_Id,Transaction_Date_Create,Transaction_Save,Transaction_Submit,Transaction_Cancel,Del,Transaction_Patch,Authulized,SOH,GardDate,Inventory,Open_Q,Close_Q,AvCost, Cost_ProductPerUnit, NetQ_Qsetup_CurrentQBase " & FieldPro & ") VALUES"
            SqlStrDetails = SqlStrDetails & "('" & Transaction_Code & "'," & Product_Id & ",'" & Product_Code & "','" & Product_Name & "'," & Order_Q & "," & Reciving_Q & "," & Invoice_Q & "," & Return_Q & "," & AvCost & "," & CostTotalLine & "," & CostCenter_Id_Frm & ",'" & CostCenter_Name_Frm & "'," & CostCenter_Id_To & ",'" & CostCenter_Name_To & "'," & Suppliers_Id_Frm & " ,'" & Suppliers_Name_Frm & "'," & Suppliers_Id_To & ",'" & Suppliers_Name_To & "','" & Supplier_Frm & "','" & Supplier_To & "'," & Patch_Ser & ",'" & Patch_Name & "','" & IsExpire & "'," & Unt_Id & ",'" & Unt_Name & "'," & Unt_GroupId & "," & Unt_Q & "," & Current_Unt_Id & ",'" & Current_Unt_Name & "'," & Current_Unt_Q & "," & NetQ_Qsetup_CurrentQ & ",7,'" & transCreate & "','" & Transaction_Save & "','" & Transaction_Submit & "','" & Transaction_Cancel & "','" & Del & "','" & Transaction_Patch & "','" & Authulized & "'," & soh & ",'" & GardDate & "',0,'" & Open_Q & "','" & Close_Q & "','" & AvCost & "','" & Cost_ProductPerUnit & "','" & NetQ_Qsetup_CurrentQBase & "'" & Prapro & ")"
            Conn.EXECUT_Txt(SqlStrDetails)

            SqlStrHistory = " INSERT INTO Transaction_Details_HistoryTbl (Transaction_Code,Product_Id,Product_Code,Product_Name,Order_Q,Reciving_Q,Invoice_Q,Return_Q,Cost_Product,CostTotalLine,CostCenter_Id_Frm,CostCenter_Name_Frm,CostCenter_Id_To,CostCenter_Name_To,Suppliers_Id_Frm,Suppliers_Name_Frm,Suppliers_Id_To,Suppliers_Name_To,Supplier_Frm,Supplier_To,Patch_Ser,Patch_Name,IsExpire,Unt_Id,Unt_Name,Unt_GroupId,Unt_Q,Current_Unt_Id,Current_Unt_Name,Current_Unt_Q,NetQ_Qsetup_CurrentQ,Transaction_Id,Transaction_Date_Create,Transaction_Save,Transaction_Submit,Transaction_Cancel,Del,Transaction_Patch,Authulized,SOH,GardDate,Inventory,Open_Q,Close_Q,AvCost, Cost_ProductPerUnit, NetQ_Qsetup_CurrentQBase" & FieldPro & ") VALUES"
            SqlStrHistory = SqlStrHistory & "('" & Transaction_Code & "'," & Product_Id & ",'" & Product_Code & "','" & Product_Name & "'," & Order_Q & "," & Reciving_Q & "," & Invoice_Q & "," & Return_Q & "," & AvCost & "," & CostTotalLine & "," & CostCenter_Id_Frm & ",'" & CostCenter_Name_Frm & "'," & CostCenter_Id_To & ",'" & CostCenter_Name_To & "'," & Suppliers_Id_Frm & " ,'" & Suppliers_Name_Frm & "'," & Suppliers_Id_To & ",'" & Suppliers_Name_To & "','" & Supplier_Frm & "','" & Supplier_To & "'," & Patch_Ser & ",'" & Patch_Name & "','" & IsExpire & "'," & Unt_Id & ",'" & Unt_Name & "'," & Unt_GroupId & "," & Unt_Q & "," & Current_Unt_Id & ",'" & Current_Unt_Name & "'," & Current_Unt_Q & "," & NetQ_Qsetup_CurrentQ & ",7,'" & transCreate & "','" & Transaction_Save & "','" & Transaction_Submit & "','" & Transaction_Cancel & "','" & Del & "','" & Transaction_Patch & "','" & Authulized & "'," & soh & ",'" & GardDate & "',0,'" & Open_Q & "','" & Close_Q & "','" & AvCost & "','" & Cost_ProductPerUnit & "','" & NetQ_Qsetup_CurrentQBase & "'" & Prapro & ")"
            Conn.EXECUT_Txt(SqlStrHistory)

            If IsProductions Then
                ClsOnHand.UpdateAvPrice(Product_Id, AvCost, AvCost, CostCenter_Id_To, Product_Code)
            End If
            'Patch_Ser,Patch_Name,

            If IsExpire = True Then
                ClsOnHand.Update_PatchTransFer(Patch_Ser, Product_Id, Product_Code, Product_Name, CostCenter_Id_To, CostCenter_Name_To, Unt_Id, Unt_Name, Unt_GroupId, Unt_Q, Current_Unt_Id, Current_Unt_Name, Current_Unt_Q, NetQ_Qsetup_CurrentQ, True)
            End If





        Else
            '//////////////////////////////// TransFer (OUT) ///////////////////////////////////////////////////////////////////
            transCreate = Transaction_Date_Create.ToString("yyyy-MM-dd HH:mm:ss")
            GardDate = Gard.ToString("yyyy-MM-dd HH:mm:ss")

            Dim CostFromId, CostToId As Integer
            Dim CostFromName, CostToName As String
            '  CostCenter_Id_Frm & ",'" & CostCenter_Name_Frm & "'," & CostCenter_Id_To & ",'" & CostCenter_Name_To
            CostFromId = CostCenter_Id_Frm
            CostFromName = CostCenter_Name_Frm
            CostToId = CostCenter_Id_To
            CostToName = CostCenter_Name_To


            CostCenter_Id_To = CostFromId
            CostCenter_Name_To = CostFromName
            CostCenter_Name_Frm = CostToName
            CostCenter_Id_Frm = CostToId

            soh = ClsOnHand.Save_Stock(Product_Id, Product_Name, Product_Code, CostCenter_Id_To, NetQ_Qsetup_CurrentQ, False, soh)

            Reciving_Q = Val(Reciving_Q) * -1
            NetQ_Qsetup_CurrentQ = Val(NetQ_Qsetup_CurrentQ) * -1
            Open_QB = 0
            Close_QBase = 0
            '///////////////////////////////////////////////////////////////////////////////
            Dt.Clear()
            Dt = ClsOnHand.Show_StockWithParam(Product_Id, CostCenter_Id_To)

            If Dt.Rows.Count = 0 Then

                Open_QB = 0

            Else

                Open_QB = Convert.ToDouble(Dt.Rows(0)("QuntityBase"))
            End If
            NetQ_Qsetup_CurrentQBase = Required_Quantity * UsedQuantity * Current_Unt_Q
            Item_Unit = Unt_Q
            ' NetQ_Qsetup_CurrentQBase = (NetQ_Qsetup_CurrentQBase * Unt_Q) 'Current_Unt_Q) / Unt_Q

            '///////////////////////////////////////////////////////////////////////////////

            Open_Q = NetQ_Qsetup_CurrentQ
            Close_Q = soh
            If IsProductions = False Then
                Open_Q = Open_Q * -1
                ' Open_QB = Open_QB * -1
            End If
            Close_QBase = Val(Open_QB) - Val(NetQ_Qsetup_CurrentQBase)
            '   Unt_Q = Unt_Q
            Open_Q = Close_Q + Open_Q

            AvCost = Cost_Product
            Cost_ProductPerUnit = Cost_Product
            ClsOnHand.BaseUniteSOH(Product_Id, Product_Code, CostCenter_Id_To, Close_QBase, Unt_Q)

            NetQ_Qsetup_CurrentQBase = Math.Abs(NetQ_Qsetup_CurrentQBase)
            NetQ_Qsetup_CurrentQBase = NetQ_Qsetup_CurrentQBase * -1
            Dim Okkay As Boolean
            Okkay = True
            SqlStrDetails = " INSERT INTO Transaction_DetailsTbl (Transaction_Code,Product_Id,Product_Code,Product_Name,Order_Q,Reciving_Q,Invoice_Q,Return_Q,Cost_Product,CostTotalLine,CostCenter_Id_Frm,CostCenter_Name_Frm,CostCenter_Id_To,CostCenter_Name_To,Suppliers_Id_Frm,Suppliers_Name_Frm,Suppliers_Id_To,Suppliers_Name_To,Supplier_Frm,Supplier_To,Patch_Ser,Patch_Name,IsExpire,Unt_Id,Unt_Name,Unt_GroupId,Unt_Q,Current_Unt_Id,Current_Unt_Name,Current_Unt_Q,NetQ_Qsetup_CurrentQ,Transaction_Id,Transaction_Date_Create,Transaction_Save,Transaction_Submit,Transaction_Cancel,Del,Transaction_Patch,Authulized,SOH,GardDate,Inventory,Open_Q,Close_Q,AvCost, Cost_ProductPerUnit, NetQ_Qsetup_CurrentQBase" & FieldPro & ") VALUES"
            SqlStrDetails = SqlStrDetails & "('" & Transaction_Code & "'," & Product_Id & ",'" & Product_Code & "','" & Product_Name & "'," & Order_Q & "," & Reciving_Q & "," & Invoice_Q & "," & Return_Q & "," & Cost_Product & "," & CostTotalLine & "," & CostCenter_Id_Frm & ",'" & CostCenter_Name_Frm & "'," & CostCenter_Id_To & ",'" & CostCenter_Name_To & "'," & Suppliers_Id_Frm & " ,'" & Suppliers_Name_Frm & "'," & Suppliers_Id_To & ",'" & Suppliers_Name_To & "','" & Supplier_Frm & "','" & Supplier_To & "'," & Patch_Ser & ",'" & Patch_Name & "','" & IsExpire & "'," & Unt_Id & ",'" & Unt_Name & "'," & Unt_GroupId & "," & Unt_Q & "," & Current_Unt_Id & ",'" & Current_Unt_Name & "'," & Current_Unt_Q & "," & NetQ_Qsetup_CurrentQ & ",7,'" & transCreate & "','" & Transaction_Save & "','" & Transaction_Submit & "','" & Transaction_Cancel & "','" & Del & "','" & Transaction_Patch & "','" & Okkay & "'," & soh & ",'" & GardDate & "',0,'" & Open_Q & "','" & Close_Q & "','" & AvCost & "','" & Cost_ProductPerUnit & "','" & NetQ_Qsetup_CurrentQBase & "'" & Prapro & ")"
            Conn.EXECUT_Txt(SqlStrDetails)

            SqlStrHistory = " INSERT INTO Transaction_Details_HistoryTbl (Transaction_Code,Product_Id,Product_Code,Product_Name,Order_Q,Reciving_Q,Invoice_Q,Return_Q,Cost_Product,CostTotalLine,CostCenter_Id_Frm,CostCenter_Name_Frm,CostCenter_Id_To,CostCenter_Name_To,Suppliers_Id_Frm,Suppliers_Name_Frm,Suppliers_Id_To,Suppliers_Name_To,Supplier_Frm,Supplier_To,Patch_Ser,Patch_Name,IsExpire,Unt_Id,Unt_Name,Unt_GroupId,Unt_Q,Current_Unt_Id,Current_Unt_Name,Current_Unt_Q,NetQ_Qsetup_CurrentQ,Transaction_Id,Transaction_Date_Create,Transaction_Save,Transaction_Submit,Transaction_Cancel,Del,Transaction_Patch,Authulized,SOH,GardDate,Inventory,Open_Q,Close_Q,AvCost, Cost_ProductPerUnit, NetQ_Qsetup_CurrentQBase" & FieldPro & ") VALUES"
            SqlStrHistory = SqlStrHistory & "('" & Transaction_Code & "'," & Product_Id & ",'" & Product_Code & "','" & Product_Name & "'," & Order_Q & "," & Reciving_Q & "," & Invoice_Q & "," & Return_Q & "," & Cost_Product & "," & CostTotalLine & "," & CostCenter_Id_Frm & ",'" & CostCenter_Name_Frm & "'," & CostCenter_Id_To & ",'" & CostCenter_Name_To & "'," & Suppliers_Id_Frm & " ,'" & Suppliers_Name_Frm & "'," & Suppliers_Id_To & ",'" & Suppliers_Name_To & "','" & Supplier_Frm & "','" & Supplier_To & "'," & Patch_Ser & ",'" & Patch_Name & "','" & IsExpire & "'," & Unt_Id & ",'" & Unt_Name & "'," & Unt_GroupId & "," & Unt_Q & "," & Current_Unt_Id & ",'" & Current_Unt_Name & "'," & Current_Unt_Q & "," & NetQ_Qsetup_CurrentQ & ",7,'" & transCreate & "','" & Transaction_Save & "','" & Transaction_Submit & "','" & Transaction_Cancel & "','" & Del & "','" & Transaction_Patch & "','" & Okkay & "'," & soh & ",'" & GardDate & "',0,'" & Open_Q & "','" & Close_Q & "','" & AvCost & "','" & Cost_ProductPerUnit & "','" & NetQ_Qsetup_CurrentQBase & "'" & Prapro & ")"
            Conn.EXECUT_Txt(SqlStrHistory)


            'Patch_Ser,Patch_Name,
            Reciving_Q = Val(Reciving_Q) * -1
            NetQ_Qsetup_CurrentQ = Val(NetQ_Qsetup_CurrentQ) * -1
            If IsExpire = True Then
                ClsOnHand.Update_PatchTransFer(Patch_Ser, Product_Id, Product_Code, Product_Name, CostCenter_Id_To, CostCenter_Name_To, Unt_Id, Unt_Name, Unt_GroupId, Unt_Q, Current_Unt_Id, Current_Unt_Name, Current_Unt_Q, NetQ_Qsetup_CurrentQ, False)
            End If



        End If

    End Sub


    Public Function Details_SaveSales(Transaction_Code As String, Product_Id As Integer, Product_Code As String, Product_Name As String, Order_Q As Double, Reciving_Q As Double, Invoice_Q As Double, Return_Q As Double, Cost_Product As Double, CostTotalLine As Double, CostCenter_Id_Frm As Integer, CostCenter_Name_Frm As String, CostCenter_Id_To As Integer, CostCenter_Name_To As String, Suppliers_Id_Frm As Integer, Suppliers_Name_Frm As String, Suppliers_Id_To As Integer, Suppliers_Name_To As String, Supplier_Frm As Boolean, Supplier_To As Boolean, Patch_Ser As Integer, Patch_Name As String, IsExpire As Boolean, Unt_Id As Integer, Unt_Name As String, Unt_GroupId As Integer, Unt_Q As Double, Current_Unt_Id As Integer, Current_Unt_Name As String, Current_Unt_Q As Double, NetQ_Qsetup_CurrentQ As Double, Transaction_Date_Create As DateTime, Transaction_Save As Boolean, Transaction_Submit As Boolean, Transaction_Cancel As Boolean, Del As Boolean, Transaction_Patch As String, Authulized As Boolean, Gard As DateTime) As Integer
        Dim Open_Q, Close_Q, AvCost, Open_QB, Close_QBase, Cost_ProductPerUnit, NetQ_Qsetup_CurrentQBase, Item_Unit As Decimal
        Dim Dt As New DataTable
        Dim soh As Decimal = 0
        '  If Transaction_Submit = True And Authulized = True Then
        Dt.Clear()
        '  Dt = ClsProduct.ShowProduct_(Product_Id)
        Dt = ClsOnHand.Show_StockWithParam(Product_Id, CostCenter_Id_To)
        Dim OldAvPrice As Decimal
        If Dt.Rows.Count = 0 Then
            OldAvPrice = 0
        Else

            OldAvPrice = Dt.Rows(0)("AvCost")
        End If
        Cost_Product = OldAvPrice
        AvCost = Cost_Product
        CostTotalLine = NetQ_Qsetup_CurrentQ * AvCost
        '//////////////////// Get Open And Close'//////////////////////////////////////////
        Dt.Clear()
        Dt = ClsOnHand.Show_StockWithParam(Product_Id, CostCenter_Id_To)

        If Dt.Rows.Count = 0 Then

            Open_QB = 0
            Open_Q = 0
        Else
            Open_Q = Convert.ToDouble(Dt.Rows(0)("Quntity"))
            Open_QB = Convert.ToDouble(Dt.Rows(0)("QuntityBase"))
        End If

        Cost_ProductPerUnit = Cost_Product
        Item_Unit = Unt_Q
        NetQ_Qsetup_CurrentQBase = Reciving_Q * Current_Unt_Q



        Close_QBase = Val(Open_QB) + Val(NetQ_Qsetup_CurrentQBase)
        Close_Q = Val(Open_Q) + Val(NetQ_Qsetup_CurrentQ)

        '////////////////////////////////////////Av Prise//////////////////////////////////
        ''Dt.Clear()
        ''   Dt = ClsProduct.ShowProduct_(Product_Id)
        ''Dt = ClsOnHand.Show_StockWithParam(Product_Id, CostCenter_Id_To)
        ''Dim OldAvPrice, NewAvPrice As Decimal
        ''If Dt.Rows.Count = 0 Then
        ''    OldAvPrice = 0
        ''Else

        ''    OldAvPrice = Dt.Rows(0)("AvCost")
        ''End If

        ''OldAvPrice = Val(OldAvPrice) * Val(Open_Q)

        ''NewAvPrice = Val(Cost_Product) * Val(NetQ_Qsetup_CurrentQ)

        ''OldAvPrice += Val(NewAvPrice)

        ''If Close_Q <> 0 Then
        ''    AvCost = OldAvPrice / Close_Q
        ''Else
        ''    Close_Q = 0
        ''    AvCost = 0
        ''End If
        ''If AvCost > 0 Then

        ''Else
        ''    AvCost = 0
        ''End If
        'MessageBox.Show(AvCost * Close_Q)
        ' AvCost = Cost_Product
        soh = ClsOnHand.Save_Stock(Product_Id, Product_Name, Product_Code, CostCenter_Id_To, NetQ_Qsetup_CurrentQ, False, soh, Cost_Product, AvCost)

        ClsOnHand.BaseUniteSOH(Product_Id, Product_Code, CostCenter_Id_To, Close_QBase, Unt_Q)
        ' End If


        Dim SqlStrDetails, transCreate, GardDate As String
        Dim SqlStrHistory As String
        'transCreate = Format(Transaction_Date_Create, "yyyy-MM-dd")

        transCreate = Transaction_Date_Create.ToString("yyyy-MM-dd HH:mm:ss")
        GardDate = Gard.ToString("yyyy-MM-dd HH:mm:ss")
        '//////////////////////////////// TransFer (IN) ///////////////////////////////////////////////////////////////////
        SqlStrDetails = " INSERT INTO Transaction_DetailsTbl (Transaction_Code,Product_Id,Product_Code,Product_Name,Order_Q,Reciving_Q,Invoice_Q,Return_Q,Cost_Product,CostTotalLine,CostCenter_Id_Frm,CostCenter_Name_Frm,CostCenter_Id_To,CostCenter_Name_To,Suppliers_Id_Frm,Suppliers_Name_Frm,Suppliers_Id_To,Suppliers_Name_To,Supplier_Frm,Supplier_To,Patch_Ser,Patch_Name,IsExpire,Unt_Id,Unt_Name,Unt_GroupId,Unt_Q,Current_Unt_Id,Current_Unt_Name,Current_Unt_Q,NetQ_Qsetup_CurrentQ,Transaction_Id,Transaction_Date_Create,Transaction_Save,Transaction_Submit,Transaction_Cancel,Del,Transaction_Patch,Authulized,SOH,GardDate,Open_Q,Close_Q,AvCost,Inventory, Cost_ProductPerUnit, NetQ_Qsetup_CurrentQBase) VALUES"
        SqlStrDetails = SqlStrDetails & "('" & Transaction_Code & "'," & Product_Id & ",'" & Product_Code & "','" & Product_Name & "'," & Order_Q & "," & Reciving_Q & "," & Invoice_Q & "," & Return_Q & "," & Cost_Product & "," & CostTotalLine & "," & CostCenter_Id_Frm & ",'" & CostCenter_Name_Frm & "'," & CostCenter_Id_To & ",'" & CostCenter_Name_To & "'," & Suppliers_Id_Frm & " ,'" & Suppliers_Name_Frm & "'," & Suppliers_Id_To & ",'" & Suppliers_Name_To & "','" & Supplier_Frm & "','" & Supplier_To & "'," & Patch_Ser & ",'" & Patch_Name & "','" & IsExpire & "'," & Unt_Id & ",'" & Unt_Name & "'," & Unt_GroupId & "," & Unt_Q & "," & Current_Unt_Id & ",'" & Current_Unt_Name & "'," & Current_Unt_Q & "," & NetQ_Qsetup_CurrentQ & ",9,'" & transCreate & "','" & Transaction_Save & "','" & Transaction_Submit & "','" & Transaction_Cancel & "','" & Del & "','" & Transaction_Patch & "','" & Authulized & "'," & soh & ",'" & GardDate & "','" & Open_Q & "','" & Close_Q & "','" & AvCost & "',0,'" & Cost_ProductPerUnit & "','" & NetQ_Qsetup_CurrentQBase & "')"
        Conn.EXECUT_Txt(SqlStrDetails)


        SqlStrHistory = " INSERT INTO Transaction_Details_HistoryTbl (Transaction_Code,Product_Id,Product_Code,Product_Name,Order_Q,Reciving_Q,Invoice_Q,Return_Q,Cost_Product,CostTotalLine,CostCenter_Id_Frm,CostCenter_Name_Frm,CostCenter_Id_To,CostCenter_Name_To,Suppliers_Id_Frm,Suppliers_Name_Frm,Suppliers_Id_To,Suppliers_Name_To,Supplier_Frm,Supplier_To,Patch_Ser,Patch_Name,IsExpire,Unt_Id,Unt_Name,Unt_GroupId,Unt_Q,Current_Unt_Id,Current_Unt_Name,Current_Unt_Q,NetQ_Qsetup_CurrentQ,Transaction_Id,Transaction_Date_Create,Transaction_Save,Transaction_Submit,Transaction_Cancel,Del,Transaction_Patch,Authulized,SOH,GardDate,Open_Q,Close_Q,AvCost,Inventory, Cost_ProductPerUnit, NetQ_Qsetup_CurrentQBase) VALUES"
        SqlStrHistory = SqlStrHistory & "('" & Transaction_Code & "'," & Product_Id & ",'" & Product_Code & "','" & Product_Name & "'," & Order_Q & "," & Reciving_Q & "," & Invoice_Q & "," & Return_Q & "," & Cost_Product & "," & CostTotalLine & "," & CostCenter_Id_Frm & ",'" & CostCenter_Name_Frm & "'," & CostCenter_Id_To & ",'" & CostCenter_Name_To & "'," & Suppliers_Id_Frm & " ,'" & Suppliers_Name_Frm & "'," & Suppliers_Id_To & ",'" & Suppliers_Name_To & "','" & Supplier_Frm & "','" & Supplier_To & "'," & Patch_Ser & ",'" & Patch_Name & "','" & IsExpire & "'," & Unt_Id & ",'" & Unt_Name & "'," & Unt_GroupId & "," & Unt_Q & "," & Current_Unt_Id & ",'" & Current_Unt_Name & "'," & Current_Unt_Q & "," & NetQ_Qsetup_CurrentQ & ",9,'" & transCreate & "','" & Transaction_Save & "','" & Transaction_Submit & "','" & Transaction_Cancel & "','" & Del & "','" & Transaction_Patch & "','" & Authulized & "'," & soh & ",'" & GardDate & "','" & Open_Q & "','" & Close_Q & "','" & AvCost & "',0, '" & Cost_ProductPerUnit & "','" & NetQ_Qsetup_CurrentQBase & "')"
        Conn.EXECUT_Txt(SqlStrHistory)

        'Patch_Ser,Patch_Name,

        If IsExpire = True And Transaction_Submit = True And Authulized = True Then
            ClsOnHand.Update_PatchTransFer(Patch_Ser, Product_Id, Product_Code, Product_Name, CostCenter_Id_To, CostCenter_Name_To, Unt_Id, Unt_Name, Unt_GroupId, Unt_Q, Current_Unt_Id, Current_Unt_Name, Current_Unt_Q, NetQ_Qsetup_CurrentQ, False)
        End If

        Dim transSer As Integer = 0
        Dim DtSer As New DataTable
        DtSer.Clear()
        DtSer = Conn.SELECT_TXT("select * from Transaction_DetailsTbl where Transaction_Code=" & Transaction_Code & " and Transaction_Id=9 ")
        'Ser
        If DtSer.Rows.Count > 0 Then
            transSer = Convert.ToInt32(DtSer.Rows(0)("Ser"))
        End If

        '//////////////////////////////// TransFer (OUT) ///////////////////////////////////////////////////////////////////
        'Dim CostFromId, CostToId As Integer
        'Dim CostFromName, CostToName As String
        ''  CostCenter_Id_Frm & ",'" & CostCenter_Name_Frm & "'," & CostCenter_Id_To & ",'" & CostCenter_Name_To
        'CostFromId = CostCenter_Id_Frm
        'CostFromName = CostCenter_Name_Frm
        'CostToId = CostCenter_Id_To
        'CostToName = CostCenter_Name_To


        'CostCenter_Id_To = CostFromId
        'CostCenter_Name_To = CostFromName
        'CostCenter_Name_Frm = CostToName
        'CostCenter_Id_Frm = CostToId

        soh = 0
        'If Transaction_Submit = True And Authulized = True Then
        '    Open_Q = 0
        '    Close_Q = 0
        '    AvCost = 0

        '    Open_QB = 0
        '    Close_QBase = 0
        '    '//////////////////// Get Open And Close'//////////////////////////////////////////
        '    Dt.Clear()
        '    Dt = ClsOnHand.Show_StockWithParam(Product_Id, CostCenter_Id_To)

        '    If Dt.Rows.Count = 0 Then
        '        Open_Q = 0
        '    Else
        '        Open_Q = Convert.ToDouble(Dt.Rows(0)("Quntity"))
        '        Open_QB = Convert.ToDouble(Dt.Rows(0)("QuntityBase"))
        '    End If
        '    NetQ_Qsetup_CurrentQBase = Reciving_Q * Current_Unt_Q
        '    Close_QBase = Val(Open_QB) - Val(NetQ_Qsetup_CurrentQBase)


        '    Close_Q = Val(Open_Q) - Val(NetQ_Qsetup_CurrentQ)
        '    '////////////////////////////////////////Av Prise//////////////////////////////////
        '    'Dt.Clear()
        '    'Dt = ClsProduct.ShowProduct_(Product_Id)

        '    'Dim OldAvPrice, NewAvPrice As Double
        '    'If Dt.Rows.Count = 0 Then
        '    '    OldAvPrice = 0
        '    'Else

        '    '    OldAvPrice = (Convert.ToDouble(Dt.Rows(0)("AvCost")))
        '    'End If

        '    'OldAvPrice = Math.Round(Val(OldAvPrice) * Val(Open_Q), 2)

        '    'NewAvPrice = Math.Round(Val(Cost_Product) * Val(NetQ_Qsetup_CurrentQ), 2)

        '    'OldAvPrice += Val(NewAvPrice)

        '    'AvCost = Math.Round(Val(OldAvPrice) / Val(Close_Q), 2)

        '    'ClsProduct.updateAvpriceAndCost(Product_Id, Cost_Product, AvCost)

        '    AvCost = Cost_Product

        'soh = ClsOnHand.Save_Stock(Product_Id, Product_Name, Product_Code, CostCenter_Id_To, NetQ_Qsetup_CurrentQ, False, soh)
        'ClsOnHand.BaseUniteSOH(Product_Id, Product_Code, CostCenter_Id_To, Close_QBase, Unt_Q)
        'End If

        'Reciving_Q = Val(Reciving_Q) * -1
        'NetQ_Qsetup_CurrentQBase = Val(NetQ_Qsetup_CurrentQBase) * -1

        'NetQ_Qsetup_CurrentQ = Val(NetQ_Qsetup_CurrentQ) * -1
        'CostTotalLine = CostTotalLine * -1

        'SqlStrDetails = " INSERT INTO Transaction_DetailsTbl (Transaction_Code,Product_Id,Product_Code,Product_Name,Order_Q,Reciving_Q,Invoice_Q,Return_Q,Cost_Product,CostTotalLine,CostCenter_Id_Frm,CostCenter_Name_Frm,CostCenter_Id_To,CostCenter_Name_To,Suppliers_Id_Frm,Suppliers_Name_Frm,Suppliers_Id_To,Suppliers_Name_To,Supplier_Frm,Supplier_To,Patch_Ser,Patch_Name,IsExpire,Unt_Id,Unt_Name,Unt_GroupId,Unt_Q,Current_Unt_Id,Current_Unt_Name,Current_Unt_Q,NetQ_Qsetup_CurrentQ,Transaction_Id,Transaction_Date_Create,Transaction_Save,Transaction_Submit,Transaction_Cancel,Del,Transaction_Patch,Authulized,SOH,GardDate,Open_Q,Close_Q,AvCost,Inventory, Cost_ProductPerUnit, NetQ_Qsetup_CurrentQBase) VALUES"
        'SqlStrDetails = SqlStrDetails & "('" & Transaction_Code & "'," & Product_Id & ",'" & Product_Code & "','" & Product_Name & "'," & Order_Q & "," & Reciving_Q & "," & Invoice_Q & "," & Return_Q & "," & Cost_Product & "," & CostTotalLine & "," & CostCenter_Id_Frm & ",'" & CostCenter_Name_Frm & "'," & CostCenter_Id_To & ",'" & CostCenter_Name_To & "'," & Suppliers_Id_Frm & " ,'" & Suppliers_Name_Frm & "'," & Suppliers_Id_To & ",'" & Suppliers_Name_To & "','" & Supplier_Frm & "','" & Supplier_To & "'," & Patch_Ser & ",'" & Patch_Name & "','" & IsExpire & "'," & Unt_Id & ",'" & Unt_Name & "'," & Unt_GroupId & "," & Unt_Q & "," & Current_Unt_Id & ",'" & Current_Unt_Name & "'," & Current_Unt_Q & "," & NetQ_Qsetup_CurrentQ & ",5,'" & transCreate & "','" & Transaction_Save & "','" & Transaction_Submit & "','" & Transaction_Cancel & "','" & Del & "','" & Transaction_Patch & "','" & Authulized & "'," & soh & ",'" & GardDate & "','" & Open_Q & "','" & Close_Q & "','" & AvCost & "',0, '" & Cost_ProductPerUnit & "','" & NetQ_Qsetup_CurrentQBase & "')"
        'Conn.EXECUT_Txt(SqlStrDetails)

        'SqlStrHistory = " INSERT INTO Transaction_Details_HistoryTbl (Transaction_Code,Product_Id,Product_Code,Product_Name,Order_Q,Reciving_Q,Invoice_Q,Return_Q,Cost_Product,CostTotalLine,CostCenter_Id_Frm,CostCenter_Name_Frm,CostCenter_Id_To,CostCenter_Name_To,Suppliers_Id_Frm,Suppliers_Name_Frm,Suppliers_Id_To,Suppliers_Name_To,Supplier_Frm,Supplier_To,Patch_Ser,Patch_Name,IsExpire,Unt_Id,Unt_Name,Unt_GroupId,Unt_Q,Current_Unt_Id,Current_Unt_Name,Current_Unt_Q,NetQ_Qsetup_CurrentQ,Transaction_Id,Transaction_Date_Create,Transaction_Save,Transaction_Submit,Transaction_Cancel,Del,Transaction_Patch,Authulized,SOH,GardDate,Open_Q,Close_Q,AvCost,Inventory, Cost_ProductPerUnit, NetQ_Qsetup_CurrentQBase) VALUES"
        'SqlStrHistory = SqlStrHistory & "('" & Transaction_Code & "'," & Product_Id & ",'" & Product_Code & "','" & Product_Name & "'," & Order_Q & "," & Reciving_Q & "," & Invoice_Q & "," & Return_Q & "," & Cost_Product & "," & CostTotalLine & "," & CostCenter_Id_Frm & ",'" & CostCenter_Name_Frm & "'," & CostCenter_Id_To & ",'" & CostCenter_Name_To & "'," & Suppliers_Id_Frm & " ,'" & Suppliers_Name_Frm & "'," & Suppliers_Id_To & ",'" & Suppliers_Name_To & "','" & Supplier_Frm & "','" & Supplier_To & "'," & Patch_Ser & ",'" & Patch_Name & "','" & IsExpire & "'," & Unt_Id & ",'" & Unt_Name & "'," & Unt_GroupId & "," & Unt_Q & "," & Current_Unt_Id & ",'" & Current_Unt_Name & "'," & Current_Unt_Q & "," & NetQ_Qsetup_CurrentQ & ",5,'" & transCreate & "','" & Transaction_Save & "','" & Transaction_Submit & "','" & Transaction_Cancel & "','" & Del & "','" & Transaction_Patch & "','" & Authulized & "'," & soh & ",'" & GardDate & "','" & Open_Q & "','" & Close_Q & "','" & AvCost & "',0, '" & Cost_ProductPerUnit & "','" & NetQ_Qsetup_CurrentQBase & "')"
        'Conn.EXECUT_Txt(SqlStrHistory)

        ''Patch_Ser,Patch_Name,
        'Reciving_Q = Val(Reciving_Q) * -1
        'NetQ_Qsetup_CurrentQ = Val(NetQ_Qsetup_CurrentQ) * -1
        'If IsExpire = True And Transaction_Submit = True And Authulized = True Then
        '    ClsOnHand.Update_PatchTransFer(Patch_Ser, Product_Id, Product_Code, Product_Name, CostCenter_Id_To, CostCenter_Name_To, Unt_Id, Unt_Name, Unt_GroupId, Unt_Q, Current_Unt_Id, Current_Unt_Name, Current_Unt_Q, NetQ_Qsetup_CurrentQ, False)
        'End If

        'If Transaction_Submit = True And Authulized = True Then
        '    ClsOnHand.Save_Stock(Product_Id, Product_Name, Product_Code, CostCenter_Id_To, NetQ_Qsetup_CurrentQ, False)
        'End If

        'If Transaction_Submit = True And Authulized = True Then
        CostcenterLinkItems(CostCenter_Id_To, CostCenter_Name_To, Product_Id, Product_Name, Product_Code)
        ' End If
        Return transSer
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    Public Sub CostcenterLinkItems(CostCenter_Id As Integer, CostCenter_Name As String, Product_Id As Integer, Product_Name As String, Product_Code As String)
        Dim Dt As New DataTable
        Dt.Clear()
        Dim SqlStr, SqlStrHistory As String

        SqlStr = "select * from ItmCostCenterLink where CostCenter_Id=" & CostCenter_Id & " and Product_Id=" & Product_Id & ""


        Dt = Conn.SELECT_TXT(SqlStr)

        If Dt.Rows.Count = 0 Then
            SqlStrHistory = "Insert Into ItmCostCenterLink (CostCenter_Id, CostCenter_Name, Product_Id, Product_Name, Product_Code) Values (" & CostCenter_Id & " ,'" & CostCenter_Name & "'," & Product_Id & ",'" & Product_Name & "','" & Product_Code & "')"
            ''Else

            'SqlStrHistory = "Update ItmCostCenterLink set Cost_Product='" & Cost_Product & "',AvCost='" & AvCost & "' where Suppliers_Id=" & Suppliers_Id & " and Product_Id=" & Product_Id & ""
            Conn.EXECUT_Txt(SqlStrHistory)
        End If


    End Sub


    Public Sub SaveSalesPos(CostCenterPOS_Id As Integer,
                            CostCenterPOS_Name As String,
                            CostCenter_Id_To As Integer,
                            CostCenter_Name_To As String,
                            Transaction_Code As Integer,
                            Product_Id As Integer,
                            Product_Code As String,
                            Product_Name As String,
                            Reciving_Q As Double,
                            Cost_Product As Double,
                            CostTotalLine As Double,
                            Patch_Ser As Integer,
                            Patch_Name As String,
                            Unt_Id As Integer,
                            Unt_Name As String,
                            Unt_GroupId As Integer,
                            Unt_Q As Double,
                            Current_Unt_Id As Integer,
                            Current_Unt_Name As String,
                            Current_Unt_Q As Double,
                            NetQ_Qsetup_CurrentQ As Double,
                            Transaction_Id As Integer,
                            Transaction_Date_CreateA As DateTime,
                            Transaction_Submit As Boolean,
                            TransactionDetails_Ser As Integer,
                            Authulized As Boolean,
                            SOH As Double,
                            Open_Q As Double,
                            Close_Q As Double,
                            Transaction_Patch As String,
                            Check_No As Integer,
                            TotalAvg As Double,
                            Is_Recipy As Boolean,
                            Is_Production As Boolean,
                            CompanyPOS_Id As Integer,
                            CompanyPOS_Name As String,
                            OutLetPOS_Id As Integer,
                            OutLetPOS_Name As String,
                            MethodOfPayment_Id As Integer,
                            MethodOfPayment_Name As String,
                            Sales_Price As Double,
                            IsExpire As Boolean,
                            ProductionCode As String)

        Cost_Product = Getproductcost(Product_Id, CostCenter_Id_To)

        TotalAvg = NetQ_Qsetup_CurrentQ * Cost_Product
        Dim Sql, Transaction_Date_Create As String
        Transaction_Date_Create = Transaction_Date_CreateA.ToString("yyyy-MM-dd HH:mm:ss")

        Sql = "   INSERT INTO Sales_POS (CostCenterPOS_Id,CostCenterPOS_Name ,CostCenter_Id_To,CostCenter_Name_To,Transaction_Code,Product_Id,Product_Code,Product_Name,Reciving_Q,Cost_Product,CostTotalLine,Patch_Ser,Patch_Name,Unt_Id,Unt_Name,Unt_GroupId,Unt_Q,Current_Unt_Id,Current_Unt_Name,Current_Unt_Q,NetQ_Qsetup_CurrentQ,Transaction_Id,Transaction_Date_Create,Transaction_Submit,TransactionDetails_Ser,Authulized,SOH,Open_Q,Close_Q,Transaction_Patch,Check_No,TotalAvg,Is_Recipy,Is_Production,CompanyPOS_Id,CompanyPOS_Name,OutLetPOS_Id,OutLetPOS_Name,MethodOfPayment_Id,MethodOfPayment_Name,Sales_Price,IsExpire,ProductionCode)"
        Sql = Sql & "    VALUES(" & CostCenterPOS_Id & ",'" & CostCenterPOS_Name & "'," & CostCenter_Id_To & ",'" & CostCenter_Name_To & "','" & Transaction_Code & "'," & Product_Id & ",'" & Product_Code & "','" & Product_Name & "','" & Reciving_Q & "','" & Cost_Product & "','" & CostTotalLine & "'," & Patch_Ser & ",'" & Patch_Name & "'," & Unt_Id & ",'" & Unt_Name & "'," & Unt_GroupId & ",'" & Unt_Q & "'," & Current_Unt_Id & " "
        Sql = Sql & " ,'" & Current_Unt_Name & "','" & Current_Unt_Q & "','" & NetQ_Qsetup_CurrentQ & "'," & Transaction_Id & ",'" & Transaction_Date_Create & "','" & Transaction_Submit & "'," & TransactionDetails_Ser & ",'" & Authulized & "','" & SOH & "','" & Open_Q & "','" & Close_Q & "','" & Transaction_Patch & "'," & Check_No & ",'" & TotalAvg & "','" & Is_Recipy & "','" & Is_Production & "'," & CompanyPOS_Id & ",'" & CompanyPOS_Name & "'," & OutLetPOS_Id & ",'" & OutLetPOS_Name & "'," & MethodOfPayment_Id & ",'" & MethodOfPayment_Name & "','" & Sales_Price & "','" & IsExpire & "','" & ProductionCode & "') "

        Conn.EXECUT_Txt(Sql)
    End Sub
    Private Function Getproductcost(Product_Id As Integer, CostCenter_Id_To As Integer) As Double
        Dim Qu As Double = 0
        Dim Dt As New DataTable
        Dt = Conn.SELECT_TXT("select * from   StockOnHandTbl WHERE  (Product_Id =" & Product_Id & ") AND (CostCenter_Id =" & CostCenter_Id_To & ")")

        If Dt.Rows.Count > 0 Then
            Qu = Convert.ToDouble(Dt.Rows(0)("AvCost"))
        End If
        Return Qu
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function
    Public Function ShowProductIdExpireDate_(Product_Id As Integer, CostCenter_Id As Integer)
        Dim DT As New DataTable
        DT.Clear()

        Dim SqlStr As String

        SqlStr = " SELECT       Patch_Ser, Patch_Name, Product_Code, Product_Name, CostCenter_Id, CostCenter_Name, Unt_Id, Unt_Name, Unt_GroupId, Unt_Q, Current_Unt_Id, Current_Unt_Name, Current_Unt_Q, "
        SqlStr = SqlStr & "  NetQ_Qsetup_CurrentQ, Prud_Date, Exp_Date, CloseOpen, usd, Product_Id "
        SqlStr = SqlStr & "   FROM Patches"

        SqlStr = SqlStr & " where Product_Id=" & Product_Id & " and CostCenter_Id=" & CostCenter_Id & " and CloseOpen=0  order by Exp_Date "
        DT = Conn.SELECT_TXT(SqlStr)
        Return DT

    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    Public Function ShowDataParametars_(Conditions As String)
        Dim sqlString As String
        sqlString = "SELECT   Product_Id, Product_Name, Product_Code, Product_BrandId, Product_BrandName, Unt_Id, Unt_Name, Unt_GroupId, Unt_Q, Depart_Id, Group_Id, SubGroup_Id, Cost_Product, AvCost, Consumption, "
        sqlString = sqlString & " SalesPrice, MinStock, MaxStock, ReOrder, Notes, IsStock, IsRecipe, IsExpire, IsProduction, IsSales, Auth "
        sqlString = sqlString & " FROM   ProductsTbl where "

        Dim DT As New DataTable
        DT.Clear()
        DT = Conn.SELECT_TXT(sqlString & Conditions & " order by Product_Code ")
        Return DT

    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    ' ===== ENHANCED RECIPE PROCESSING METHODS =====

    ''' <summary>
    ''' Enhanced recursive recipe processing with multi-level ingredient dependencies
    ''' </summary>
    Public Function ProcessRecipeRecursive(productId As Integer, requiredQuantity As Decimal,
                                         costCenterIds As List(Of Integer),
                                         Optional maxRecursionDepth As Integer = 10) As RecipeCalculationResult

        Dim startTime = DateTime.Now
        Dim result = ProcessRecipeRecursiveInternal(productId, requiredQuantity, costCenterIds, 0, maxRecursionDepth, New HashSet(Of Integer))
        result.ProcessingTime = DateTime.Now - startTime
        Return result
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    ''' <summary>
    ''' Process a recipe recursively, handling nested production products
    ''' </summary>
    ''' <param name="productId">The ID of the product to process</param>
    ''' <param name="requiredQuantity">Quantity needed</param>
    ''' <param name="costCenterIds">List of cost center IDs to check for ingredients</param>
    ''' <param name="currentDepth">Current recursion depth</param>
    ''' <param name="maxDepth">Maximum recursion depth to prevent infinite loops</param>
    ''' <param name="processedProducts">Set of products already processed in this branch</param>
    ''' <param name="parentProductId">ID of the parent product in the recipe tree</param>
    ''' <param name="ancestorProducts">List of all ancestor products in the recipe tree</param>
    ''' <returns>Recipe calculation result with all deductions and nested production requirements</returns>
    Public Function ProcessRecipeRecursive(productId As Integer, requiredQuantity As Decimal,
                                         costCenterIds As List(Of Integer),
                                         Optional currentDepth As Integer = 0,
                                         Optional maxDepth As Integer = 10,
                                         Optional parentProductId As Integer = 0) As RecipeCalculationResult
        Dim startTime As DateTime = DateTime.Now
        Dim processedProducts As New HashSet(Of Integer)
        Dim ancestorProducts As New List(Of Integer)
        
        Dim result = ProcessRecipeRecursiveInternal(productId, requiredQuantity, costCenterIds, 
                                                  currentDepth, maxDepth, processedProducts, 
                                                  parentProductId, ancestorProducts)
        
        result.ProcessingTime = DateTime.Now - startTime
        result.RecursiveDepth = currentDepth
        Return result
    End Function

    Private Function ProcessRecipeRecursiveInternal(productId As Integer, requiredQuantity As Decimal,
                                                  costCenterIds As List(Of Integer),
                                                  currentDepth As Integer, maxDepth As Integer,
                                                  processedProducts As HashSet(Of Integer),
                                                  parentProductId As Integer,
                                                  ancestorProducts As List(Of Integer)) As RecipeCalculationResult

        Dim result As New RecipeCalculationResult With {
            .Success = True,
            .TotalCost = 0,
            .IngredientDeductions = New List(Of IngredientDeduction),
            .ProductionRequired = New List(Of ProductionRequirement),
            .ErrorMessage = ""
        }

        ' Check for infinite recursion
        If currentDepth > maxDepth Then
            result.Success = False
            result.ErrorMessage = "Maximum recursion depth exceeded for product ID: " & productId
            Return result
        End If

        ' Check for circular dependencies
        If processedProducts.Contains(productId) Then
            result.Success = False
            result.ErrorMessage = "Circular dependency detected for product ID: " & productId
            Return result
        End If

        processedProducts.Add(productId)

        Try
            ' Check for circular dependencies or maximum recursion depth
            If ancestorProducts.Contains(productId) Then
                Dim result As New RecipeCalculationResult()
                result.Success = False
                result.ErrorMessage = $"Circular dependency detected for product ID {productId}. Recipe chain: {String.Join(" -> ", ancestorProducts)} -> {productId}"
                Return result
            End If
            
            If currentDepth > maxDepth Then
                Dim result As New RecipeCalculationResult()
                result.Success = False
                result.ErrorMessage = $"Maximum recursion depth of {maxDepth} exceeded while processing product ID {productId}."
                Return result
            End If
            
            ' Add current product to the ancestor list for this branch
            ancestorProducts.Add(productId)
            
            ' Get recipe ingredients for this product
            Dim recipeIngredients = ShowWithProduct_IdAll(productId)

            ' Get product details
            Dim productDetails = GetProductDetails(productId)
            Dim productName As String = ""
            Dim productCode As String = ""
            
            If productDetails IsNot Nothing AndAlso productDetails.Rows.Count > 0 Then
                productName = productDetails.Rows(0)("Product_Name").ToString()
                productCode = productDetails.Rows(0)("Product_Code").ToString()
            End If
            
            If recipeIngredients.Rows.Count = 0 Then
                ' This is a base ingredient, try to deduct from inventory
                Dim deductionResult = DeductFromInventoryEnhanced(productId, requiredQuantity, costCenterIds, currentDepth)
                If deductionResult.Success Then
                    result.IngredientDeductions.AddRange(deductionResult.IngredientDeductions)
                    result.TotalCost += deductionResult.TotalCost
                Else
                    result.Success = False
                    result.ErrorMessage = deductionResult.ErrorMessage
                End If

                processedProducts.Remove(productId)
                Return result
            End If

            ' Process each ingredient in the recipe
            For Each ingredientRow As DataRow In recipeIngredients.Rows
                Dim ingredientId As Integer = Convert.ToInt32(ingredientRow("Recipe_Product_Id"))
                Dim ingredientQuantity As Decimal = Convert.ToDecimal(ingredientRow("UsedQuantity")) * requiredQuantity

                ' Check if this ingredient is also a recipe (recursive call)
                Dim ingredientRecipe = ShowWithProduct_IdAll(ingredientId)

                If ingredientRecipe.Rows.Count > 0 Then
                    ' This ingredient is also a recipe - recurse
                    Dim subResult = ProcessRecipeRecursiveInternal(ingredientId, ingredientQuantity,
                                                                 costCenterIds, currentDepth + 1,
                                                                 maxDepth, New HashSet(Of Integer)(processedProducts))

                    If subResult.Success Then
                        result.IngredientDeductions.AddRange(subResult.IngredientDeductions)
                        result.ProductionRequired.AddRange(subResult.ProductionRequired)
                        result.TotalCost += subResult.TotalCost
                    Else
                        ' If we can't produce this ingredient, check if we can deduct from inventory
                        Dim directDeduction = DeductFromInventoryEnhanced(ingredientId, ingredientQuantity, costCenterIds, currentDepth + 1)
                        If directDeduction.Success Then
                            result.IngredientDeductions.AddRange(directDeduction.IngredientDeductions)
                            result.TotalCost += directDeduction.TotalCost
                        Else
                            ' Add to production requirements
                            Dim prodReq As New ProductionRequirement With {
                                .ProductId = ingredientId,
                                .ProductCode = ingredientRow("Recipe_Product_Code").ToString(),
                                .ProductName = ingredientRow("Recipe_Product_Name").ToString(),
                                .RequiredQuantity = ingredientQuantity,
                                .Recipe = New List(Of IngredientDeduction),
                                .EstimatedCost = Convert.ToDecimal(ingredientRow("Cost_Product")) * ingredientQuantity,
                                .Priority = currentDepth
                            }
                            result.ProductionRequired.Add(prodReq)
                        End If
                    End If
                Else
                    ' This is a base ingredient
                    Dim deductionResult = DeductFromInventoryEnhanced(ingredientId, ingredientQuantity, costCenterIds, currentDepth + 1)
                    If deductionResult.Success Then
                        result.IngredientDeductions.AddRange(deductionResult.IngredientDeductions)
                        result.TotalCost += deductionResult.TotalCost
                    Else
                        result.Success = False
                        result.ErrorMessage = "Insufficient inventory for ingredient: " & ingredientRow("Recipe_Product_Name").ToString()
                        processedProducts.Remove(productId)
                        Return result
                    End If
                End If
            Next

        Catch ex As Exception
            result.Success = False
            result.ErrorMessage = "Error processing recipe for product ID " & productId & ": " & ex.Message
        End Try

        processedProducts.Remove(productId)
        Return result
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    ''' <summary>
    ''' Enhanced inventory deduction with smart cost center selection
    ''' </summary>
    Private Function DeductFromInventoryEnhanced(productId As Integer, requiredQuantity As Decimal,
                                               costCenterIds As List(Of Integer),
                                               recipeLevel As Integer) As RecipeCalculationResult

        Dim result As New RecipeCalculationResult With {
            .Success = False,
            .TotalCost = 0,
            .IngredientDeductions = New List(Of IngredientDeduction),
            .ErrorMessage = ""
        }

        Dim remainingQuantity As Decimal = requiredQuantity

        ' Get optimized cost center priorities
        Dim prioritizedCostCenters = GetCostCenterPriorities(productId, costCenterIds)

        ' Try each cost center in priority order
        For Each costCenter In prioritizedCostCenters
            If remainingQuantity <= 0 Then Exit For

            Dim availableStock = ClsOnHand.Show_StockWithParam(productId, costCenter.CostCenterId)
            If availableStock.Rows.Count > 0 Then
                Dim availableQty As Decimal = Convert.ToDecimal(availableStock.Rows(0)("Quntity"))
                Dim unitCost As Decimal = Convert.ToDecimal(availableStock.Rows(0)("AvCost"))

                If availableQty > 0 Then
                    Dim deductQty As Decimal = Math.Min(remainingQuantity, availableQty)

                    ' Create deduction record
                    Dim deduction As New IngredientDeduction With {
                        .ProductId = productId,
                        .ProductCode = availableStock.Rows(0)("Product_Code").ToString(),
                        .ProductName = availableStock.Rows(0)("Product_Name").ToString(),
                        .CostCenterId = costCenter.CostCenterId,
                        .CostCenterName = costCenter.CostCenterName,
                        .RequiredQuantity = requiredQuantity,
                        .AvailableQuantity = availableQty,
                        .DeductedQuantity = deductQty,
                        .UnitCost = unitCost,
                        .TotalCost = deductQty * unitCost,
                        .IsProduced = False,
                        .RecipeLevel = recipeLevel,
                        .PatchSer = 0,
                        .PatchName = ""
                    }

                    result.IngredientDeductions.Add(deduction)
                    result.TotalCost += deduction.TotalCost
                    remainingQuantity -= deductQty
                End If
            End If
        Next

        If remainingQuantity <= 0 Then
            result.Success = True
        Else
            result.ErrorMessage = $"Insufficient inventory. Required: {requiredQuantity}, Available: {requiredQuantity - remainingQuantity}"
        End If

        Return result
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    ''' <summary>
    ''' Gets cost centers prioritized by availability and cost
    ''' </summary>
    Private Function GetCostCenterPriorities(productId As Integer, costCenterIds As List(Of Integer)) As List(Of CostCenterPriority)
        Dim priorities As New List(Of CostCenterPriority)

        For Each costCenterId In costCenterIds
            Try
                Dim stockInfo = ClsOnHand.Show_StockWithParam(productId, costCenterId)
                Dim costCenterInfo = Conn.SELECT_TXT($"SELECT CostCenter_Name FROM CostCenterTbl WHERE CostCenter_Id = {costCenterId}")

                If stockInfo.Rows.Count > 0 AndAlso costCenterInfo.Rows.Count > 0 Then
                    Dim priority As New CostCenterPriority With {
                        .CostCenterId = costCenterId,
                        .CostCenterName = costCenterInfo.Rows(0)("CostCenter_Name").ToString(),
                        .AvailableQuantity = Convert.ToDecimal(stockInfo.Rows(0)("Quntity")),
                        .AvgCost = Convert.ToDecimal(stockInfo.Rows(0)("AvCost")),
                        .Priority = 1 ' Default priority
                    }
                    priorities.Add(priority)
                End If
            Catch ex As Exception
                ' Continue with next cost center if error occurs
                Continue For
            End Try
        Next

        ' Sort by available quantity (highest first) then by cost (lowest first)
        priorities = priorities.OrderByDescending(Function(p) p.AvailableQuantity).ThenBy(Function(p) p.AvgCost).ToList()

        Return priorities
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    ''' <summary>
    ''' Enhanced sales processing with recipe recursion
    ''' </summary>
    Public Function ProcessEnhancedSalesTransaction(salesData As DataTable, costCenterIds As List(Of Integer)) As RecipeCalculationResult
        Dim overallResult As New RecipeCalculationResult With {
            .Success = True,
            .TotalCost = 0,
            .IngredientDeductions = New List(Of IngredientDeduction),
            .ProductionRequired = New List(Of ProductionRequirement),
            .ErrorMessage = ""
        }

        Try
            ' Get next transaction code
            Dim transactionCode = GetNextTransactionCode()
            Dim transactionPatch = Guid.NewGuid().ToString()

            ' Create transaction header
            Head_Insert(transactionCode, costCenterIds.First(), 0, GetCostCenterName(costCenterIds.First()),
                       "Enhanced Sales Transaction", "", 0, 0, 0, False, True, 1, 0, 0, 0, True,
                       DateTime.Now, DateTime.Now, transactionPatch, 1, "", 9)

            Dim totalAmount As Decimal = 0

            ' Process each sales item
            For Each row As DataRow In salesData.Rows
                Dim productCode = row("plu").ToString()
                Dim quantity = Convert.ToDecimal(row("Qty"))
                Dim unitPrice = Convert.ToDecimal(row("price"))

                ' Get product information
                Dim productInfo = GetProductByCode(productCode)
                If productInfo.Rows.Count = 0 Then
                    Continue For
                End If

                Dim productId = Convert.ToInt32(productInfo.Rows(0)("Product_Id"))
                Dim productName = productInfo.Rows(0)("Product_Name").ToString()
                Dim isRecipe = Convert.ToBoolean(productInfo.Rows(0)("IsRecipe"))

                If isRecipe Then
                    ' Process recipe item with recursion
                    Dim recipeResult = ProcessRecipeRecursive(productId, quantity, costCenterIds)

                    If recipeResult.Success Then
                        ' Record ingredient deductions
                        For Each ingredient In recipeResult.IngredientDeductions
                            ' Update inventory
                            ClsOnHand.Save_Stock(ingredient.ProductId, ingredient.ProductName, ingredient.ProductCode,
                                               ingredient.CostCenterId, ingredient.DeductedQuantity, False, 0,
                                               ingredient.UnitCost)

                            ' Record transaction detail
                            Details_Insert(transactionCode, ingredient.ProductId, ingredient.ProductCode, ingredient.ProductName,
                                         0, 0, ingredient.DeductedQuantity, 0, ingredient.UnitCost, ingredient.TotalCost,
                                         ingredient.CostCenterId, ingredient.CostCenterName, 0, "", 0, "", 0, "", "", "",
                                         0, "", False, 0, "", 0, 0, 0, "", 0, 0, 9, DateTime.Now, False, True, False, False,
                                         transactionPatch, True, 0, DateTime.Now, 0, 0, ingredient.UnitCost, False,
                                         ingredient.UnitCost, 0)
                        Next

                        overallResult.IngredientDeductions.AddRange(recipeResult.IngredientDeductions)
                        overallResult.ProductionRequired.AddRange(recipeResult.ProductionRequired)
                        totalAmount += recipeResult.TotalCost
                    Else
                        overallResult.ErrorMessage += $"Recipe processing failed for {productName}: {recipeResult.ErrorMessage}; "
                    End If
                Else
                    ' Process regular item
                    Dim regularResult = DeductFromInventoryEnhanced(productId, quantity, costCenterIds, 0)

                    If regularResult.Success Then
                        For Each deduction In regularResult.IngredientDeductions
                            ' Update inventory
                            ClsOnHand.Save_Stock(deduction.ProductId, deduction.ProductName, deduction.ProductCode,
                                               deduction.CostCenterId, deduction.DeductedQuantity, False, 0,
                                               deduction.UnitCost)

                            ' Record transaction detail
                            Details_Insert(transactionCode, deduction.ProductId, deduction.ProductCode, deduction.ProductName,
                                         0, 0, deduction.DeductedQuantity, 0, deduction.UnitCost, deduction.TotalCost,
                                         deduction.CostCenterId, deduction.CostCenterName, 0, "", 0, "", 0, "", "", "",
                                         0, "", False, 0, "", 0, 0, 0, "", 0, 0, 9, DateTime.Now, False, True, False, False,
                                         transactionPatch, True, 0, DateTime.Now, 0, 0, deduction.UnitCost, False,
                                         deduction.UnitCost, 0)
                        Next

                        overallResult.IngredientDeductions.AddRange(regularResult.IngredientDeductions)
                        totalAmount += regularResult.TotalCost
                    Else
                        overallResult.ErrorMessage += $"Inventory deduction failed for {productName}: {regularResult.ErrorMessage}; "
                    End If
                End If
            Next

            ' Update transaction header with total amount
            UpdateHeadAmount(transactionCode, 9, totalAmount)
            overallResult.TotalCost = totalAmount

        Catch ex As Exception
            overallResult.Success = False
            overallResult.ErrorMessage = "Enhanced sales processing failed: " & ex.Message
        End Try

        Return overallResult
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    ''' <summary>
    ''' Validates if a recipe can be fully processed with available inventory
    ''' </summary>
    Public Function ValidateRecipeAvailability(productId As Integer, requiredQuantity As Decimal,
                                             costCenterIds As List(Of Integer)) As Boolean

        Dim result = ProcessRecipeRecursive(productId, requiredQuantity, costCenterIds)
        Return result.Success AndAlso result.ProductionRequired.Count = 0
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    ''' <summary>
    ''' Gets the total cost for producing a recipe
    ''' </summary>
    Public Function CalculateRecipeCost(productId As Integer, quantity As Decimal,
                                      costCenterIds As List(Of Integer)) As Decimal

        Dim result = ProcessRecipeRecursive(productId, quantity, costCenterIds)
        If result.Success Then
            Return result.TotalCost
        Else
            Return 0
        End If
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    ''' <summary>
    ''' Simulates production for recipe products when inventory is insufficient
    ''' </summary>
    Public Function SimulateProduction(productionRequirements As List(Of ProductionRequirement),
                                      costCenterIds As List(Of Integer)) As RecipeCalculationResult

        Dim result As New RecipeCalculationResult With {
            .Success = True,
            .TotalCost = 0,
            .IngredientDeductions = New List(Of IngredientDeduction),
            .ProductionRequired = New List(Of ProductionRequirement),
            .ErrorMessage = ""
        }

        Try
            For Each prodReq In productionRequirements
                ' Calculate what's needed to produce this item
                Dim productionResult = ProcessRecipeRecursive(prodReq.ProductId, prodReq.RequiredQuantity, costCenterIds)

                If productionResult.Success Then
                    ' Add the produced item to inventory (simulation)
                    Dim producedItem As New IngredientDeduction With {
                        .ProductId = prodReq.ProductId,
                        .ProductCode = prodReq.ProductCode,
                        .ProductName = prodReq.ProductName,
                        .CostCenterId = costCenterIds.First(),
                        .CostCenterName = GetCostCenterName(costCenterIds.First()),
                        .RequiredQuantity = prodReq.RequiredQuantity,
                        .AvailableQuantity = 0,
                        .DeductedQuantity = prodReq.RequiredQuantity,
                        .UnitCost = productionResult.TotalCost / prodReq.RequiredQuantity,
                        .TotalCost = productionResult.TotalCost,
                        .IsProduced = True,
                        .RecipeLevel = 0
                    }

                    result.IngredientDeductions.Add(producedItem)
                    result.IngredientDeductions.AddRange(productionResult.IngredientDeductions)
                    result.TotalCost += productionResult.TotalCost
                Else
                    result.Success = False
                    result.ErrorMessage += $"Production simulation failed for {prodReq.ProductName}: {productionResult.ErrorMessage}; "
                End If
            Next

        Catch ex As Exception
            result.Success = False
            result.ErrorMessage = "Production simulation error: " & ex.Message
        End Try

        Return result
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    ''' <summary>
    ''' Processes production orders and updates inventory
    ''' </summary>
    Public Function ProcessProductionOrder(productId As Integer, quantity As Decimal, costCenterId As Integer) As RecipeCalculationResult
        Dim result As New RecipeCalculationResult With {
            .Success = False,
            .TotalCost = 0,
            .IngredientDeductions = New List(Of IngredientDeduction),
            .ProductionRequired = New List(Of ProductionRequirement),
            .ErrorMessage = ""
        }

        Try
            ' Get next transaction code for production
            Dim transactionCode = GetNextTransactionCodeForProduction()
            Dim transactionPatch = Guid.NewGuid().ToString()

            ' Create production transaction header
            Head_Insert(transactionCode, costCenterId, 0, GetCostCenterName(costCenterId),
                       "Production Order", "", 0, 0, 0, False, True, 1, 0, 0, 0, True,
                       DateTime.Now, DateTime.Now, transactionPatch, 1, "", 7)

            ' Process recipe to get ingredients needed
            Dim recipeResult = ProcessRecipeRecursive(productId, quantity, New List(Of Integer) From {costCenterId})

            If recipeResult.Success Then
                ' Deduct ingredients from inventory
                For Each ingredient In recipeResult.IngredientDeductions
                    ' Update inventory (deduct ingredients)
                    ClsOnHand.Save_Stock(ingredient.ProductId, ingredient.ProductName, ingredient.ProductCode,
                                       ingredient.CostCenterId, ingredient.DeductedQuantity, False, 0,
                                       ingredient.UnitCost)

                    ' Record ingredient usage in transaction
                    Details_Insert(transactionCode, ingredient.ProductId, ingredient.ProductCode, ingredient.ProductName,
                                 0, 0, ingredient.DeductedQuantity, 0, ingredient.UnitCost, ingredient.TotalCost,
                                 ingredient.CostCenterId, ingredient.CostCenterName, 0, "", 0, "", 0, "", "", "",
                                 0, "", False, 0, "", 0, 0, 0, "", 0, 0, 7, DateTime.Now, False, True, False, False,
                                 transactionPatch, True, 0, DateTime.Now, 0, 0, ingredient.UnitCost, False,
                                 ingredient.UnitCost, 0)
                Next

                ' Add produced quantity to inventory
                Dim productInfo = GetProductById(productId)
                If productInfo.Rows.Count > 0 Then
                    Dim productCode = productInfo.Rows(0)("Product_Code").ToString()
                    Dim productName = productInfo.Rows(0)("Product_Name").ToString()
                    Dim unitCost = recipeResult.TotalCost / quantity

                    ' Update inventory (add produced item)
                    ClsOnHand.Save_Stock(productId, productName, productCode, costCenterId, quantity, True, 0, unitCost)

                    ' Record production output in transaction
                    Details_Insert(transactionCode, productId, productCode, productName,
                                 0, quantity, 0, 0, unitCost, recipeResult.TotalCost,
                                 0, "", costCenterId, GetCostCenterName(costCenterId), 0, "", 0, "", "", "",
                                 0, "", False, 0, "", 0, 0, 0, "", 0, 0, 7, DateTime.Now, False, True, False, False,
                                 transactionPatch, True, 0, DateTime.Now, 0, 0, unitCost, False,
                                 unitCost, 0)
                End If

                ' Update transaction header with total amount
                UpdateHeadAmount(transactionCode, 7, recipeResult.TotalCost)

                result = recipeResult
                result.Success = True
            Else
                result.ErrorMessage = "Production failed: " & recipeResult.ErrorMessage
            End If

        Catch ex As Exception
            result.Success = False
            result.ErrorMessage = "Production order processing failed: " & ex.Message
        End Try

        Return result
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    ' Helper methods
    Private Function GetNextTransactionCode() As Integer
        Dim dt = Conn.SELECT_TXT("SELECT ISNULL(MAX(Transaction_Code), 0) + 1 as NextCode FROM Transaction_HeadTbl WHERE Transaction_Id = 9")
        Return Convert.ToInt32(dt.Rows(0)("NextCode"))
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    Private Function GetNextTransactionCodeForProduction() As Integer
        Dim dt = Conn.SELECT_TXT("SELECT ISNULL(MAX(Transaction_Code), 0) + 1 as NextCode FROM Transaction_HeadTbl WHERE Transaction_Id = 7")
        Return Convert.ToInt32(dt.Rows(0)("NextCode"))
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    Private Function GetProductByCode(productCode As String) As DataTable
        Return Conn.SELECT_TXT($"SELECT * FROM ProductsTbl WHERE Product_Code = '{productCode}'")
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    Private Function GetProductById(productId As Integer) As DataTable
        Return Conn.SELECT_TXT($"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}")
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    Private Function GetCostCenterName(costCenterId As Integer) As String
        Dim dt = Conn.SELECT_TXT($"SELECT CostCenter_Name FROM CostCenterTbl WHERE CostCenter_Id = {costCenterId}")
        Return If(dt.Rows.Count > 0, dt.Rows(0)("CostCenter_Name").ToString(), "Unknown")
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    ''' <summary>
    ''' Gets recipe analysis with cost breakdown
    ''' </summary>
    Public Function GetRecipeAnalysis(productId As Integer, quantity As Decimal, costCenterIds As List(Of Integer)) As RecipeCalculationResult
        Dim result = ProcessRecipeRecursive(productId, quantity, costCenterIds)

        ' Add additional analysis information
        If result.Success Then
            ' Calculate profit margins, efficiency metrics, etc.
            Dim productInfo = GetProductById(productId)
            If productInfo.Rows.Count > 0 Then
                Dim salesPrice = Convert.ToDecimal(productInfo.Rows(0)("SalesPrice"))
                Dim grossProfit = (salesPrice * quantity) - result.TotalCost
                ' Additional analysis can be added here
            End If
        End If

        Return result
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

    ''' <summary>
    ''' Checks inventory levels and suggests production orders
    ''' </summary>
    Public Function CheckInventoryAndSuggestProduction(costCenterIds As List(Of Integer)) As List(Of ProductionRequirement)
        Dim suggestions As New List(Of ProductionRequirement)

        Try
            ' Get all recipe products with low stock
            Dim lowStockProducts = Conn.SELECT_TXT("
                SELECT p.Product_Id, p.Product_Code, p.Product_Name, p.MinStock,
                       ISNULL(SUM(soh.Quntity), 0) as CurrentStock
                FROM ProductsTbl p
                LEFT JOIN StockOnHandTbl soh ON p.Product_Id = soh.Product_Id
                WHERE p.IsRecipe = 1 AND p.MinStock > 0
                GROUP BY p.Product_Id, p.Product_Code, p.Product_Name, p.MinStock
                HAVING ISNULL(SUM(soh.Quntity), 0) < p.MinStock")

            For Each row As DataRow In lowStockProducts.Rows
                Dim productId = Convert.ToInt32(row("Product_Id"))
                Dim currentStock = Convert.ToDecimal(row("CurrentStock"))
                Dim minStock = Convert.ToDecimal(row("MinStock"))
                Dim requiredQuantity = minStock - currentStock

                If requiredQuantity > 0 Then
                    Dim suggestion As New ProductionRequirement With {
                        .ProductId = productId,
                        .ProductCode = row("Product_Code").ToString(),
                        .ProductName = row("Product_Name").ToString(),
                        .RequiredQuantity = requiredQuantity,
                        .Priority = 1,
                        .EstimatedCost = CalculateRecipeCost(productId, requiredQuantity, costCenterIds)
                    }
                    suggestions.Add(suggestion)
                End If
            Next

        Catch ex As Exception
            ' Log error but continue
        End Try

        Return suggestions
    End Function
    
    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function
    
    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()
        
        Try
            ' Get all cost centers with this product
            Dim sql As String = $"SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity, 
                                ISNULL(p.Priority, 999) AS Priority, ISNULL(p.IsDefault, 0) AS IsDefault
                                FROM CostCenterLinkPOS cc 
                                LEFT JOIN StockOnHandTbl_POS soh ON cc.CostCenter_Id = soh.CostCenter_Id 
                                                                AND soh.Product_Id = {productId}
                                LEFT JOIN CostCenterPriorities p ON cc.CostCenter_Id = p.CostCenter_Id
                                WHERE soh.Product_Id = {productId}
                                ORDER BY p.Priority, cc.CostCenter_Id"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            For Each row As DataRow In dt.Rows
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(row("CostCenter_Id"))
                costCenter.CostCenterName = row("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(row("Quntity"))
                costCenter.Priority = Convert.ToInt32(row("Priority"))
                costCenter.IsDefault = Convert.ToBoolean(row("IsDefault"))
                
                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost
                
                result.Add(costCenter)
            Next
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try
        
        Return result
    End Function
    
    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0
        
        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try
        
        Return avgCost
    End Function
    
    ''' <summary>
    ''' Intelligently distribute ingredient deduction across multiple cost centers
    ''' </summary>
    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, 
                                               costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity
        
        ' Get all available cost centers with this product
        Dim availableCostCenters = GetAvailableCostCenters(productId)
        
        ' Sort by priority (lower number = higher priority)
        availableCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()
        
        ' Filter by the provided cost center IDs if specified
        If costCenterIds IsNot Nothing AndAlso costCenterIds.Count > 0 Then
            availableCostCenters = availableCostCenters.Where(Function(cc) costCenterIds.Contains(cc.CostCenterId)).ToList()
        End If
        
        ' First pass: try to fulfill from cost centers with sufficient quantity
        For Each costCenter In availableCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If
            
            If costCenter.AvailableQuantity >= remainingQuantity Then
                ' This cost center can fulfill the entire remaining requirement
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = remainingQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = remainingQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority
                
                result.Add(deduction)
                remainingQuantity = 0
                Exit For
            End If
        Next
        
        ' Second pass: if still not fulfilled, take partial quantities
        If remainingQuantity > 0 Then
            For Each costCenter In availableCostCenters
                If remainingQuantity <= 0 Then
                    Exit For
                End If
                
                If costCenter.AvailableQuantity > 0 Then
                    ' Take whatever is available from this cost center
                    Dim deductionQty As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)
                    
                    Dim deduction As New CostCenterDeduction()
                    deduction.CostCenterId = costCenter.CostCenterId
                    deduction.CostCenterName = costCenter.CostCenterName
                    deduction.DeductedQuantity = deductionQty
                    deduction.UnitCost = costCenter.AvgCost
                    deduction.TotalCost = deductionQty * costCenter.AvgCost
                    deduction.Priority = costCenter.Priority
                    
                    result.Add(deduction)
                    remainingQuantity -= deductionQty
                End If
            Next
        End If
        
        Return result
    End Function

End Class
