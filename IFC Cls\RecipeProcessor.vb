Imports System.Data.SqlClient
Imports System.Collections.Generic
Imports System.Linq

Public Class RecipeProcessor
    Private IFC_Cls As IFC_Transaction
    Private Conn As Conn_Cls

    Public Sub New()
        IFC_Cls = New IFC_Transaction()
        Conn = New Conn_Cls()
    End Sub

    ''' <summary>
    ''' Process a recipe recursively, handling production products as ingredients
    ''' </summary>
    ''' <param name="productId">The main product ID to produce</param>
    ''' <param name="quantity">Quantity to produce</param>
    ''' <param name="costCenterId">Target cost center for production</param>
    ''' <returns>True if successful, False if errors occurred</returns>
    Public Function ProcessRecipe(productId As Integer, quantity As Decimal, costCenterId As Integer) As Boolean
        Try
            ' Get available cost centers
            Dim costCenterIds As New List(Of Integer)()
            costCenterIds.Add(costCenterId)

            ' Process the recipe recursively
            Dim result = IFC_Cls.ProcessRecipeRecursive(productId, quantity, costCenterId)

            If Not result.Success Then
                ' Log error and return false
                Console.WriteLine($"Error processing recipe: {result.ErrorMessage}")
                Return False
            End If

            ' Process all required productions first (from deepest level up)
            Dim sortedProductions = result.ProductionRequired.OrderByDescending(Function(p) p.RecursionLevel).ToList()

            For Each production In sortedProductions
                ' Create production transaction
                CreateProductionTransaction(production, costCenterId)
            Next

            ' Process main product deductions
            For Each ingredient In result.IngredientDeductions
                If Not ingredient.IsRecursiveIngredient Then
                    ' Deduct from inventory
                    DeductIngredient(ingredient)
                End If
            Next

            Return True
        Catch ex As Exception
            Console.WriteLine($"Error in ProcessRecipe: {ex.Message}")
            Return False
        End Try
    End Function

    ''' <summary>
    ''' Create a production transaction for a product and its ingredients
    ''' </summary>
    Private Sub CreateProductionTransaction(production As IFC_Transaction.ProductionRequirement, targetCostCenterId As Integer)
        Try
            ' Get transaction code for production
            Dim transCode = IFC_Cls.GetTransCodeProduction

            ' Create transaction header
            IFC_Cls.Head_Insert(transCode, targetCostCenterId, 0, "", "Production", "",
                               production.EstimatedCost, 0, production.EstimatedCost,
                               True, True, 1, 0, 0, 0, True, DateTime.Now, DateTime.Now,
                               Guid.NewGuid().ToString(), 1, "", 7)

            ' Process all ingredients
            For Each ingredient In production.Recipe
                ' Create negative transaction for ingredient (consumption)
                For Each costCenterDeduction In ingredient.SourceCostCenters
                    IFC_Cls.Details_Save(transCode, ingredient.ProductId, ingredient.ProductCode,
                                       ingredient.ProductName, 0, costCenterDeduction.DeductedQuantity * -1,
                                       costCenterDeduction.DeductedQuantity * -1, 0,
                                       costCenterDeduction.UnitCost, costCenterDeduction.TotalCost,
                                       costCenterDeduction.CostCenterId, costCenterDeduction.CostCenterName,
                                       targetCostCenterId, "", 0, "", 0, "", False, False,
                                       0, "", False, 0, "", 0, 0, 0, "", 0,
                                       costCenterDeduction.DeductedQuantity * -1, DateTime.Now,
                                       True, True, False, False, Guid.NewGuid().ToString(),
                                       True, DateTime.Now, False, costCenterDeduction.UnitCost)
                Next
            Next

            ' Create positive transaction for produced item
            IFC_Cls.Details_Save(transCode, production.ProductId, production.ProductCode,
                               production.ProductName, 0, production.RequiredQuantity,
                               production.RequiredQuantity, 0,
                               production.EstimatedCost / production.RequiredQuantity, production.EstimatedCost,
                               0, "", targetCostCenterId, "", 0, "", 0, "", False, False,
                               0, "", False, 0, "", 0, 0, 0, "", 0,
                               production.RequiredQuantity, DateTime.Now,
                               True, True, False, False, Guid.NewGuid().ToString(),
                               True, DateTime.Now, True, production.EstimatedCost / production.RequiredQuantity)

        Catch ex As Exception
            Console.WriteLine($"Error in CreateProductionTransaction: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' Deduct an ingredient from inventory
    ''' </summary>
    Private Sub DeductIngredient(ingredient As IFC_Transaction.IngredientDeduction)
        Try
            For Each costCenterDeduction In ingredient.SourceCostCenters
                ' Update stock on hand
                Dim currentQty = GetCurrentQuantity(ingredient.ProductId, costCenterDeduction.CostCenterId)
                Dim newQty = currentQty - costCenterDeduction.DeductedQuantity

                UpdateStockQuantity(ingredient.ProductId, costCenterDeduction.CostCenterId, newQty)
            Next
        Catch ex As Exception
            Console.WriteLine($"Error in DeductIngredient: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' Get current quantity of a product in a cost center
    ''' </summary>
    Private Function GetCurrentQuantity(productId As Integer, costCenterId As Integer) As Decimal
        Dim qty As Decimal = 0

        Try
            ' Convert costCenterId to string as required by GetSOHDataGetting method
            Dim dt = IFC_Cls.GetSOHDataGetting(productId, costCenterId.ToString())

            If dt.Rows.Count > 0 Then
                qty = Convert.ToDecimal(dt.Rows(0)("Quntity"))
            End If
        Catch ex As Exception
            Console.WriteLine($"Error in GetCurrentQuantity: {ex.Message}")
        End Try

        Return qty
    End Function

    ''' <summary>
    ''' Update stock quantity for a product in a cost center
    ''' </summary>
    Private Sub UpdateStockQuantity(productId As Integer, costCenterId As Integer, newQuantity As Decimal)
        Try
            Dim sql As String = $"UPDATE StockOnHandTbl SET Quntity = {newQuantity} " &
                              $"WHERE Product_Id = {productId} AND CostCenter_Id = {costCenterId}"

            Conn.EXECUT_Txt(sql)
        Catch ex As Exception
            Console.WriteLine($"Error in UpdateStockQuantity: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' Check if a product has a recipe (is a production product)
    ''' </summary>
    Public Function IsProductionProduct(productId As Integer) As Boolean
        Try
            Dim dt = IFC_Cls.ShowWithProduct_IdAll(productId)
            Return dt.Rows.Count > 0
        Catch ex As Exception
            Console.WriteLine($"Error in IsProductionProduct: {ex.Message}")
            Return False
        End Try
    End Function

    ''' <summary>
    ''' Get all production products that use this product as an ingredient
    ''' </summary>
    Public Function GetParentProducts(productId As Integer) As List(Of Integer)
        Dim result As New List(Of Integer)()

        Try
            Dim sql As String = $"SELECT DISTINCT Product_Id FROM Recipe_ProductsTbl " &
                              $"WHERE Recipe_Product_Id = {productId}"

            Dim dt = Conn.SELECT_TXT(sql)

            For Each row As DataRow In dt.Rows
                result.Add(Convert.ToInt32(row("Product_Id")))
            Next
        Catch ex As Exception
            Console.WriteLine($"Error in GetParentProducts: {ex.Message}")
        End Try

        Return result
    End Function
End Class
