Imports System.Data.SqlClient

Public Class OnHand_Cls
    Dim Conn As New Conn_Cls

    Public Function Save_Stock(Product_Id As Integer, Product_Name As String, Product_Code As String, CostCenter_Id As Integer, Quntity As Double, Plus As Boolean, SOH As Double,
                               Optional Cost_ProductPerUnit As Double = 0, Optional AvCost As Decimal = 0) As Decimal
        Dim Dt As New DataTable
        Dim Q As Double
        Dim reslt As Boolean = False

        Dt.Clear()
        Dt = Show_StockWithParam(Product_Id, CostCenter_Id)

        If Dt.Rows.Count = 0 And Plus = False Then Return reslt
        If Dt.Rows.Count = 0 Then
            Q = 0
        Else
            Q = Convert.ToDouble(Dt.Rows(0)("Quntity"))
        End If

        If Val(Q) < Val(Quntity) And Plus = False Then
            SOH = 0
            Return SOH
        End If


        Dim FieldAvAndCost, ParameterAVCost As String

        FieldAvAndCost = ""
        ParameterAVCost = ""
        If Cost_ProductPerUnit > 0 Then
            ' FieldAvAndCost = ",Cost_ProductPerUnit"
            ParameterAVCost = ",Cost_Product='" & Cost_ProductPerUnit & "'"

        End If

        If AvCost > 0 Then
            ' FieldAvAndCost = FieldAvAndCost & ",AvCost"
            ParameterAVCost = ParameterAVCost & ",AvCost='" & AvCost & "'"

        End If

        Dim SqlStr As String

        If Plus Then

            '***********************Update Insert ***********************
            If Dt.Rows.Count = 0 Then
                FieldAvAndCost = ""
                ParameterAVCost = ""
                If Cost_ProductPerUnit > 0 Then
                    FieldAvAndCost = ",Cost_Product"
                    ParameterAVCost = ",'" & Cost_ProductPerUnit & "'"

                End If

                If AvCost > 0 Then
                    FieldAvAndCost = FieldAvAndCost & ",AvCost"
                    ParameterAVCost = ParameterAVCost & ",'" & AvCost & "'"

                End If

                SqlStr = "INSERT INTO StockOnHandTbl(Product_Id,Product_Name,Product_Code,CostCenter_Id,Quntity " & FieldAvAndCost & ") VALUES (" & Product_Id & ",'" & Product_Name & "','" & Product_Code & "'," & CostCenter_Id & ",'" & Quntity & "' " & ParameterAVCost & ")"

                Conn.EXECUT_Txt(SqlStr)
                SOH = Quntity
                reslt = True
            Else
                ' reslt = True
                ' Q = Convert.ToDouble(Dt.Rows(0)("Quntity"))
                Q = Val(Q) + Val(Quntity)
                SqlStr = "UPDATE StockOnHandTbl SET Quntity ='" & Q & "' " & ParameterAVCost & "  where Product_Id=" & Product_Id & " and CostCenter_Id=" & CostCenter_Id & ""
                Conn.EXECUT_Txt(SqlStr)
                SOH = Q
                reslt = True
            End If
            '*************************************************************************
        Else
            '//////////// If Plus=False


            Q = Val(Q) - Val(Quntity)
            SqlStr = "UPDATE StockOnHandTbl SET Quntity ='" & Q & "'  " & ParameterAVCost & "  where Product_Id=" & Product_Id & " and CostCenter_Id=" & CostCenter_Id & ""
            Conn.EXECUT_Txt(SqlStr)
            SOH = Q
            reslt = True
        End If

        Return SOH
    End Function
    Public Sub BaseUniteSOH(Product_Id As Integer, Product_Code As String, CostCenter_Id_To As Integer, Close_QBase As Double, Unt_Q As Double)

        Dim Sql As String
        Sql = "Update StockOnHandTbl set QuntityBase='" & Close_QBase & "', Item_Unit='" & Unt_Q & "' where Product_Id=" & Product_Id & " and Product_Code='" & Product_Code & "' and CostCenter_Id=" & CostCenter_Id_To & ""

        Conn.EXECUT_Txt(Sql)
    End Sub

    Public Sub UpdateAvPrice(Product_Id As Integer, Cost_Product As Double, AvCost As Double, CostCenter_Id_To As Integer, Product_Code As String)
        Dim SqlString, sql As String
        SqlString = "UPDATE ProductsTbl SET Cost_Product ='" & Cost_Product & "',AvCost ='" & AvCost & "' WHERE Product_Id=" & Product_Id & ""

        Conn.EXECUT_Txt(SqlString)

        sql = "Update StockOnHandTbl set AvCost='" & AvCost & "', Cost_Product='" & Cost_Product & "' where Product_Id=" & Product_Id & " and Product_Code='" & Product_Code & "' and CostCenter_Id=" & CostCenter_Id_To & ""

        Conn.EXECUT_Txt(sql)
    End Sub



    Public Function Show_StockWithParam(Product_Id As Integer, CostCenter_Id As Integer) As DataTable
        Dim SqlStr As String
        Dim Dt As New DataTable
        SqlStr = "SELECT Ser, Product_Id, Product_Name, Product_Code, CostCenter_Id, Quntity, QuntityBase, Item_Unit, AvCost,Cost_Product FROM  StockOnHandTbl where Product_Id=" & Product_Id & " and CostCenter_Id=" & CostCenter_Id & " "
        Dt = Conn.SELECT_TXT(SqlStr)
        Return Dt
    End Function

    Public Sub Update_PatchTransFer(Patch_Ser As Integer, Product_Id As Integer, Product_Code As String, Product_Name As String, CostCenter_Id As Integer, CostCenter_Name As String, Unt_Id As Integer, Unt_Name As String, Unt_GroupId As Integer, Unt_Q As Double, Current_Unt_Id As Integer, Current_Unt_Name As String, Current_Unt_Q As Integer, NetQ_Qsetup_CurrentQ As Double, Plus As Boolean)
        Dim SqlStr As String
        Dim Dt As New DataTable
        Dim Okay As Boolean = False

        'Okay = CheckPatchQ(Patch_Ser, Product_Id, CostCenter_Id, NetQ_Qsetup_CurrentQ)
        'If Okay = False Then Return
        Dt.Clear()

        Dt = GetPatchQ(Patch_Ser, Product_Id, CostCenter_Id, NetQ_Qsetup_CurrentQ)
        If Plus = False And Dt.Rows.Count = 0 Then Return

        If Plus = True And Dt.Rows.Count = 0 Then
            Dt.Clear()
            Dt = ShowSer_(Patch_Ser)

            Create_PathchWithCost(Dt.Rows(0)("Patch_Name"), Dt.Rows(0)("Prud_Date"), Dt.Rows(0)("Exp_Date"), NetQ_Qsetup_CurrentQ, CostCenter_Id, CostCenter_Name, Unt_Id, Unt_Name, Unt_GroupId, Unt_Q, Current_Unt_Id, Current_Unt_Name, Current_Unt_Q, Product_Id, Product_Code, Product_Name)
            Return
        End If
        NetQ_Qsetup_CurrentQ = Math.Abs(NetQ_Qsetup_CurrentQ)
        If Plus = True Then
            NetQ_Qsetup_CurrentQ = Val(Dt.Rows(0)("NetQ_Qsetup_CurrentQ")) + Val(NetQ_Qsetup_CurrentQ)
            SqlStr = "UPDATE Patches  SET Product_Id=" & Product_Id & " ,Product_Code ='" & Product_Code & "' ,Product_Name ='" & Product_Name & "' ,CostCenter_Id =" & CostCenter_Id & " ,CostCenter_Name ='" & CostCenter_Name & "' ,Unt_Id =" & Unt_Id & ",Unt_Name ='" & Unt_Name & "' ,Unt_GroupId =" & Unt_GroupId & ",Unt_Q =" & Unt_Q & " ,Current_Unt_Id =" & Current_Unt_Id & ",Current_Unt_Name ='" & Current_Unt_Name & "' ,Current_Unt_Q =" & Current_Unt_Q & ",NetQ_Qsetup_CurrentQ =" & NetQ_Qsetup_CurrentQ & "  WHERE Patch_Ser=" & Patch_Ser & ""
            Conn.EXECUT_Txt(SqlStr)
        Else
            NetQ_Qsetup_CurrentQ = Val(NetQ_Qsetup_CurrentQ) * -1
            NetQ_Qsetup_CurrentQ = Val(Dt.Rows(0)("NetQ_Qsetup_CurrentQ")) + Val(NetQ_Qsetup_CurrentQ)

            Dim OpenClsoe As Boolean = False
            If NetQ_Qsetup_CurrentQ = 0 Then OpenClsoe = True
            SqlStr = "UPDATE Patches  SET Product_Id=" & Product_Id & ",CloseOpen='" & OpenClsoe & "' ,Product_Code ='" & Product_Code & "' ,Product_Name ='" & Product_Name & "' ,CostCenter_Id =" & CostCenter_Id & " ,CostCenter_Name ='" & CostCenter_Name & "' ,Unt_Id =" & Unt_Id & ",Unt_Name ='" & Unt_Name & "' ,Unt_GroupId =" & Unt_GroupId & ",Unt_Q =" & Unt_Q & " ,Current_Unt_Id =" & Current_Unt_Id & ",Current_Unt_Name ='" & Current_Unt_Name & "' ,Current_Unt_Q =" & Current_Unt_Q & ",NetQ_Qsetup_CurrentQ =" & NetQ_Qsetup_CurrentQ & "  WHERE Patch_Ser=" & Patch_Ser & ""
            Conn.EXECUT_Txt(SqlStr)
        End If



    End Sub

    Public Sub Create_PathchWithCost(Patch_Name As String, Prud_Date As DateTime, Exp_Date As DateTime, NetQ_Qsetup_CurrentQ As Double, CostCenter_Id As Integer, CostCenter_Name As String, Unt_Id As Integer, Unt_Name As String, Unt_GroupId As Integer, Unt_Q As Double, Current_Unt_Id As Integer, Current_Unt_Name As String, Current_Unt_Q As Double, Product_Id As Integer, Product_Code As String, Product_Name As String)

        Dim SqlStr, DateExp_Date, DatePrud_Date As String
        DatePrud_Date = Format(Prud_Date, "yyyy-MM-dd")
        DateExp_Date = Format(Exp_Date, "yyyy-MM-dd")


        SqlStr = "INSERT INTO Patches (Patch_Name,Prud_Date,Exp_Date ,CloseOpen,usd,NetQ_Qsetup_CurrentQ,CostCenter_Id,CostCenter_Name,Unt_Id,Unt_Name,Unt_GroupId,Unt_Q,Current_Unt_Id,Current_Unt_Name,Current_Unt_Q,Product_Id,Product_Code,Product_Name) VALUES"
        SqlStr = SqlStr & " ('" & Patch_Name & "','" & DatePrud_Date & "','" & DateExp_Date & "',0,0," & NetQ_Qsetup_CurrentQ & "," & CostCenter_Id & ",'" & CostCenter_Name & "'," & Unt_Id & ",'" & Unt_Name & "'," & Unt_GroupId & "," & Unt_Q & "," & Current_Unt_Id & ",'" & Current_Unt_Name & "'," & Current_Unt_Q & "," & Product_Id & ",'" & Product_Code & "','" & Product_Name & "')"

        Conn.EXECUT_Txt(SqlStr)
    End Sub
    Public Function GetPatchQ(Patch_Ser As Integer, Product_Id As Integer, CostCenter_Id As Integer, NetQ_Qsetup_CurrentQ As Double) As DataTable
        Dim Dt As New DataTable
        Dt.Clear()

        Dim SqlStr As String

        SqlStr = "select * from Patches where Patch_Ser=" & Patch_Ser & " and Product_Id=" & Product_Id & " and CostCenter_Id=" & CostCenter_Id & " and CloseOpen=0"
        Dt = Conn.SELECT_TXT(SqlStr)


        Return Dt
    End Function
    Public Function ShowSer_(Patch_Ser As Integer)
        Dim DT As New DataTable
        DT.Clear()

        Dim SqlStr As String

        SqlStr = " SELECT       Patch_Ser, Patch_Name, Product_Code, Product_Name, CostCenter_Id, CostCenter_Name, Unt_Id, Unt_Name, Unt_GroupId, Unt_Q, Current_Unt_Id, Current_Unt_Name, Current_Unt_Q, "
        SqlStr = SqlStr & "  NetQ_Qsetup_CurrentQ, Prud_Date, Exp_Date, CloseOpen, usd, Product_Id"
        SqlStr = SqlStr & "   FROM Patches "

        SqlStr = SqlStr & " where Patch_Ser=" & Patch_Ser & "  and CloseOpen=0 "
        DT = Conn.SELECT_TXT(SqlStr)
        Return DT

    End Function
    Public Sub UpdateStockOnHand(productId As Integer, costCenterId As Integer, newQuantity As Decimal)
        Try
            Dim dt As New DataTable()
            dt = Show_StockWithParam(productId, costCenterId)

            Dim sqlStr As String = ""

            If dt.Rows.Count = 0 Then
                ' Insert new record if it doesn't exist
                sqlStr = "INSERT INTO StockOnHandTbl_POS (Product_Id, CostCenter_Id, Quntity) "
                sqlStr += "VALUES (@ProductId, @CostCenterId, @Quantity)"
            Else
                ' Update existing record
                sqlStr = "UPDATE StockOnHandTbl_POS SET Quntity = @Quantity "
                sqlStr += "WHERE Product_Id = @ProductId AND CostCenter_Id = @CostCenterId"
            End If

            Conn.OpenConnection()
            Dim cmd As New SqlCommand(sqlStr, Conn.SqlConn)
            cmd.Parameters.AddWithValue("@ProductId", productId)
            cmd.Parameters.AddWithValue("@CostCenterId", costCenterId)
            cmd.Parameters.AddWithValue("@Quantity", newQuantity)
            cmd.ExecuteNonQuery()
            Conn.CloseConnection()
        Catch ex As Exception
            Console.WriteLine($"Error updating stock on hand: {ex.Message}")
        End Try
    End Sub
End Class
