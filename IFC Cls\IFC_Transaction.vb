Imports System.Data.SqlClient
Imports System.Collections.Generic
Imports System.Linq
Imports System.Data

Public Class IFC_Transaction
    Dim Conn As New Conn_Cls
    'Dim ClsOnHand As New Cls_StockOnHand
    'Dim Clsprod As New Cls_Products
    'Dim ClsPatch As New Cls_Patch
    Dim ClsOnHand As New OnHand_Cls

    ' Enhanced structures for recipe processing
    Public Class RecipeCalculationResult
        Public Success As Boolean = True
        Public TotalCost As Decimal = 0
        Public IngredientDeductions As New List(Of IngredientDeduction)()
        Public ErrorMessage As String = ""
        Public ProductionRequired As New List(Of ProductionRequirement)()
        Public ProcessingTime As TimeSpan = TimeSpan.Zero
        Public RecursiveDepth As Integer = 0
        Public TotalIngredientsProcessed As Integer = 0

        Public Shared Widening Operator CType(v As RecipeCalculationResult) As Boolean
            Return v.Success
        End Operator

        Public Shared Operator Not(v As RecipeCalculationResult) As Boolean
            Return Not v.Success
        End Operator
    End Class

    Public Class IngredientDeduction
        Public ProductId As Integer = 0
        Public ProductCode As String = ""
        Public ProductName As String = ""
        Public CostCenterId As Integer = 0
        Public CostCenterName As String = ""
        Public RequiredQuantity As Decimal = 0
        Public AvailableQuantity As Decimal = 0
        Public DeductedQuantity As Decimal = 0
        Public UnitCost As Decimal = 0
        Public TotalCost As Decimal = 0
        Public PatchSer As Integer = 0
        Public PatchName As String = ""
        Public IsProduced As Boolean = False
        Public RecipeLevel As Integer = 0
        Public SourceCostCenters As New List(Of CostCenterDeduction)()
        Public IsRecursiveIngredient As Boolean = False
        Public ParentProductId As Integer = 0
    End Class

    Public Class CostCenterDeduction
        Public CostCenterId As Integer = 0
        Public CostCenterName As String = ""
        Public DeductedQuantity As Decimal = 0
        Public UnitCost As Decimal = 0
        Public TotalCost As Decimal = 0
        Public Priority As Integer = 0
    End Class

    Public Class ProductionRequirement
        Public ProductId As Integer = 0
        Public ProductCode As String = ""
        Public ProductName As String = ""
        Public RequiredQuantity As Decimal = 0
        Public Recipe As New List(Of IngredientDeduction)()
        Public EstimatedCost As Decimal = 0
        Public Priority As Integer = 0
        Public RecursionLevel As Integer = 0
        Public ParentProductIds As New List(Of Integer)()
    End Class

    Public Class CostCenterPriority
        Public CostCenterId As Integer = 0
        Public CostCenterName As String = ""
        Public Priority As Integer = 0
        Public AvailableQuantity As Decimal = 0
        Public AvgCost As Decimal = 0
        Public IsDefault As Boolean = False
    End Class

    Public Function LoadPosDataOnLine(Fromdate As DateTime, Todate As DateTime, Optional IsLoding As Boolean = False) As DataTable
        Dim SqlStr As String = ""
        Dim CompanyPOS_Id, OutLetPOS_Id, CostCenterPOS_Id, FoDates, ToDates, SqlPul As String
        CompanyPOS_Id = ""
        OutLetPOS_Id = ""
        CostCenterPOS_Id = ""
        SqlPul = ""
        If IsLoding Then SqlPul = " and plu=0 "
        Dim DtScm As New DataTable
        DtScm = Conn.SELECT_TXT(" select * from POSSetting ")
        'If DtScm.Rows.Count > 0 Then
        For R As Integer = 0 To DtScm.Rows.Count - 1


            If CompanyPOS_Id = "" Then
                CompanyPOS_Id = DtScm.Rows(R)("Company_IdPOS")
            Else
                CompanyPOS_Id = CompanyPOS_Id & "," & DtScm.Rows(R)("Company_IdPOS")
            End If


            If OutLetPOS_Id = "" Then
                OutLetPOS_Id = DtScm.Rows(R)("Brand_IdPOS")
            Else
                OutLetPOS_Id = OutLetPOS_Id & "," & DtScm.Rows(R)("Brand_IdPOS")
            End If


            If CostCenterPOS_Id = "" Then
                CostCenterPOS_Id = DtScm.Rows(R)("CostCenter_IdPOS")
            Else
                CostCenterPOS_Id = CostCenterPOS_Id & "," & DtScm.Rows(R)("CostCenter_IdPOS")
            End If
        Next

        FoDates = Format(Fromdate, "yyyy/MM/dd 00:00:00")
        ToDates = Format(Todate, "yyyy/MM/dd 23:59:59")
        If Conn.POS = "Matrix POS" Then
            SqlStr = " select * from v_ProcIFC "
            SqlStr = SqlStr & " where mandant in (" & CompanyPOS_Id & ") and outlet in (" & OutLetPOS_Id & ") and center in (" & CostCenterPOS_Id & ") "
            SqlStr = SqlStr & " And IsIFCDone=0  "
            SqlStr = SqlStr & SqlPul & "  ORDER BY mandant, outlet, center,plu "
        End If


        If Conn.POS = "Smart POS" Then
            CheckExistV_ProcIFC()
            SqlStr = " select * from v_ProcIFC "
            SqlStr = SqlStr & " where mandant in (" & CompanyPOS_Id & ") and outlet in (" & OutLetPOS_Id & ") and center in (" & CostCenterPOS_Id & ") "
            SqlStr = SqlStr & " And IsIFCDone=0  "
            SqlStr = SqlStr & SqlPul & "  ORDER BY mandant, outlet, center,plu "
        End If

        Dim Dt As New DataTable
        Dt.Clear()
        Dt = Conn.SELECT_TXTPOS(SqlStr)

        Return Dt
    End Function

    ''' <summary>
    ''' Get product details by ID
    ''' </summary>
    Public Function GetProductDetails(productId As Integer) As DataTable
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Id = {productId}"
        Return Conn.SELECT_TXT(sql)
    End Function

    ''' <summary>
    ''' Get all available cost centers for a product with their current stock levels
    ''' </summary>
    Public Function GetAvailableCostCenters(productId As Integer) As List(Of CostCenterPriority)
        Dim result As New List(Of CostCenterPriority)()

        Try
            ' Get all cost centers that have this product in stock
            Dim sql As String = "SELECT cc.CostCenter_Id, cc.CostCenter_Name, soh.Quntity "
            sql += "FROM CostCenterTbl cc "
            sql += "INNER JOIN StockOnHandTbl soh ON cc.CostCenter_Id = soh.CostCenter_Id "
            sql += "WHERE soh.Product_Id = @ProductId AND soh.Quntity > 0 AND cc.IsUse = 1 "
            sql += "ORDER BY cc.CostCenter_Id"

            Conn.OpenConnection()
            Dim cmd As New SqlCommand(sql, Conn.SqlConn)
            cmd.Parameters.AddWithValue("@ProductId", productId)

            Dim reader = cmd.ExecuteReader()
            Dim priority As Integer = 0

            While reader.Read()
                Dim costCenter As New CostCenterPriority()
                costCenter.CostCenterId = Convert.ToInt32(reader("CostCenter_Id"))
                costCenter.CostCenterName = reader("CostCenter_Name").ToString()
                costCenter.AvailableQuantity = Convert.ToDecimal(reader("Quntity"))
                costCenter.Priority = priority  ' Assign priority based on order (first has highest priority)
                costCenter.IsDefault = (priority = 0)  ' First cost center is default

                ' Get average cost for this product in this cost center
                Dim avgCost As Decimal = GetAverageCost(productId, costCenter.CostCenterId)
                costCenter.AvgCost = avgCost

                result.Add(costCenter)
                priority += 1
            End While

            reader.Close()
            Conn.CloseConnection()
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting available cost centers: {ex.Message}")
        End Try

        Return result
    End Function

    ''' <summary>
    ''' Get the average cost of a product in a specific cost center
    ''' </summary>
    Public Function ProcessEnhancedSalesTransaction(productId As Integer, quantity As Decimal, costCenterId As Integer) As Boolean
        Dim result As Boolean = False

        Try
            ' Get product details
            Dim productDt As DataTable = GetProductDetails(productId)
            If productDt.Rows.Count = 0 Then
                Console.WriteLine($"Product with ID {productId} not found")
                Return False
            End If

            ' Get product information
            Dim productRow As DataRow = productDt.Rows(0)
            Dim productCode As String = productRow("Product_Code").ToString()
            Dim productName As String = productRow("Product_Name").ToString()

            ' Get cost center details
            Dim costCenterDt As DataTable = GetCostCenterDataTrue(costCenterId)
            If costCenterDt.Rows.Count = 0 Then
                Console.WriteLine($"Cost center with ID {costCenterId} not found")
                Return False
            End If

            Dim costCenterName As String = costCenterDt.Rows(0)("CostCenter_Name").ToString()

            ' Get average cost
            Dim avgCost As Decimal = GetAverageCost(productId, costCenterId)

            ' Generate transaction code
            Dim transCode As String = GetTransCodeSales

            ' Create transaction header
            Head_Insert(transCode, costCenterId, 0, "", "Enhanced Sales Transaction", "",
                       Convert.ToDouble(quantity * avgCost), 0, Convert.ToDouble(quantity * avgCost),
                       True, True, 1, 0, 0, Convert.ToDouble(quantity * avgCost),
                       False, DateTime.Now, DateTime.Now, "", 0, "", 9)  ' 9 is for sales transaction

            ' Create transaction details
            Details_Save(transCode, productId, productCode, productName, 0, Convert.ToDouble(quantity), 0, 0,
                       Convert.ToDouble(avgCost), Convert.ToDouble(quantity * avgCost),
                       0, "", costCenterId, costCenterName, 0, "", 0, "",
                       False, False, 0, "", False, 0, "", 0, 0, 0, "", 0, 0,
                       DateTime.Now, True, True, False, False, "", False, DateTime.Now)

            ' Update stock quantity
            DeleteQuantityNow(productId, costCenterId, quantity)

            result = True
        Catch ex As Exception
            Console.WriteLine($"Error processing enhanced sales transaction: {ex.Message}")
        End Try

        Return result
    End Function

    Public Function GetAverageCost(productId As Integer, costCenterId As Integer) As Decimal
        Dim avgCost As Decimal = 0

        Try
            Dim sql As String = $"SELECT TOP 1 AvCost FROM Transaction_DetailsTbl 
                               WHERE Product_Id = {productId} 
                               AND CostCenter_Id_To = {costCenterId} 
                               ORDER BY Transaction_Date_Create DESC"

            Dim dt As DataTable = Conn.SELECT_TXT(sql)

            If dt.Rows.Count > 0 Then
                avgCost = Convert.ToDecimal(dt.Rows(0)("AvCost"))
            End If
        Catch ex As Exception
            ' Log error
            Console.WriteLine($"Error getting average cost: {ex.Message}")
        End Try

        Return avgCost
    End Function


    ' This method has been replaced by the implementation below with availableCostCenters parameter
    ' Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal,
    '                                          costCenterIds As List(Of Integer)) As List(Of CostCenterDeduction)
    '    ' Implementation moved to overload with availableCostCenters parameter
    ' End Function


    ' Methods with corrected signatures based on usage in IFC_Main_Frm.vb
    Public Function GetRecipeAnalysis(productId As Integer, quantity As Decimal, costCenterIds As List(Of Integer)) As RecipeCalculationResult
        ' Implementation
        Return New RecipeCalculationResult()
    End Function

    Public Function ValidateRecipeAvailability(productId As Integer, quantity As Decimal, costCenterIds As List(Of Integer)) As RecipeCalculationResult
        ' Implementation
        Return New RecipeCalculationResult()
    End Function

    Public Function GetProductByCode(productCode As String) As DataTable
        ' Implementation
        Dim sql As String = $"SELECT * FROM ProductsTbl WHERE Product_Code = '{productCode}'"
        Return Conn.SELECT_TXT(sql)
    End Function


    Public Sub UpdateHeadAmount(transactionCode As String, amount As Decimal, Optional taxAmount As Decimal = 0, Optional totalAmount As Decimal = 0)
        Try
            Dim sql As String = "UPDATE Transaction_HeadTbl SET Amount_Bill = @Amount, "
            sql += "Tax_Bill = @Tax, Total_Amount = @Total "
            sql += "WHERE Transaction_Code = @TransCode"

            Conn.OpenConnection()
            Dim cmd As New SqlCommand(sql, Conn.SqlConn)
            cmd.Parameters.AddWithValue("@Amount", amount)
            cmd.Parameters.AddWithValue("@Tax", taxAmount)
            cmd.Parameters.AddWithValue("@Total", If(totalAmount = 0, amount + taxAmount, totalAmount))
            cmd.Parameters.AddWithValue("@TransCode", transactionCode)
            cmd.ExecuteNonQuery()
            Conn.CloseConnection()
        Catch ex As Exception
            Console.WriteLine($"Error updating head amount: {ex.Message}")
        End Try
    End Sub

    Public Sub UpdateCHDIDOnline(chDID As Integer, isIFCDone As Boolean)
        Try
            Dim sql As String = "UPDATE v_ProcIFC SET IsIFCDone = @IsIFCDone WHERE ChDID = @ChDID"

            Conn.OpenConnection()
            Dim cmd As New SqlCommand(sql, Conn.SqlConn)
            cmd.Parameters.AddWithValue("@IsIFCDone", isIFCDone)
            cmd.Parameters.AddWithValue("@ChDID", chDID)
            cmd.ExecuteNonQuery()
            Conn.CloseConnection()
        Catch ex As Exception
            Console.WriteLine($"Error updating CHDID online: {ex.Message}")
        End Try
    End Sub

    Public Sub SaveSalesPos(CostCenterPOS_Id As Integer, CostCenterPOS_Name As String, CostCenter_Id_To As Integer, CostCenter_Name_To As String, Transaction_Code As Integer, Product_Id As Integer, Product_Code As String, Product_Name As String, Reciving_Q As Object, Cost_Product As Object, CostTotalLine As Object, Patch_Ser As Integer, Patch_Name As String, Unt_Id As Integer, Unt_Name As String, Unt_GroupId As Integer, Unt_Q As Object, Current_Unt_Id As Integer, Current_Unt_Name As String, Current_Unt_Q As Object, NetQ_Qsetup_CurrentQ As Object, TransId As Integer, Transaction_Date_Create As Object, Transaction_Submit As Object, Serr As Integer, Authulized As Object, SOH As Object, Open_Q As Object, Close_Q As Object, PatchTransSales As String, Check_No As Integer, TotalAvg As Object, Is_Recipy As Object, Is_Production As Object, CompanyPOS_Id As Integer, CompanyPOS_Name As String, OutLetPOS_Id As Integer, OutLetPOS_Name As String, MethodOfPayment_Id As Integer, MethodOfPayment_Name As String, Sales_Price As Object, IsExpire As Object, ProductionCode As Object)
        Try
            ' Generate a unique transaction code for the sales transaction
            Dim transCode As String = GetTransCodeSales

            ' Create transaction header
            Head_Insert(transCode, CostCenter_Id_To, 0, "", "Sales from POS", "", 0, 0, 0, True, False, 1, 0, 0, 0, False, DateTime.Now, DateTime.Now, "", 0, "", Transaction_Code)

            ' Save the sales details
            Dim quantity As Double = Convert.ToDouble(Reciving_Q)
            Dim cost As Double = Convert.ToDouble(Cost_Product)
            Dim totalCost As Double = Convert.ToDouble(CostTotalLine)

            Details_Save(transCode, Product_Id, Product_Code, Product_Name, 0, quantity, 0, 0, cost, totalCost,
                        CostCenterPOS_Id, CostCenterPOS_Name, CostCenter_Id_To, CostCenter_Name_To,
                        0, "", 0, "", False, False, Patch_Ser, Patch_Name, Convert.ToBoolean(IsExpire), Unt_Id, Unt_Name, Unt_GroupId, Convert.ToDouble(Unt_Q), Current_Unt_Id, Current_Unt_Name, Convert.ToDouble(Current_Unt_Q), Convert.ToDouble(NetQ_Qsetup_CurrentQ),
                        Convert.ToDateTime(Transaction_Date_Create), True, Convert.ToBoolean(Transaction_Submit), False, False, PatchTransSales, Convert.ToBoolean(Authulized), DateTime.Now)

            ' Update stock on hand
            Dim currentStock As Decimal = GetQuantityNow(Product_Id, CostCenter_Id_To, Product_Code, quantity)

            ' Update stock quantity
            ClsOnHand.UpdateStockOnHand(Product_Id, CostCenter_Id_To, currentStock + Convert.ToDecimal(quantity))

            ' Update the total amount in the transaction header
            UpdateHeadAmount(transCode, Convert.ToDecimal(totalCost))
        Catch ex As Exception
            Console.WriteLine($"Error saving sales POS: {ex.Message}")
        End Try
    End Sub

    Public Function Details_SaveSales(Transaction_Code As String, Product_Id As Integer, Product_Code As String, Product_Name As String, Order_Q As Double, Reciving_Q As Double, Invoice_Q As Double, Return_Q As Double, Cost_Product As Double, CostTotalLine As Double, CostCenter_Id_Frm As Integer, CostCenter_Name_Frm As String, CostCenter_Id_To As Integer, CostCenter_Name_To As String, Suppliers_Id_Frm As Integer, Suppliers_Name_Frm As String, Suppliers_Id_To As Integer, Suppliers_Name_To As String, Supplier_Frm As Boolean, Supplier_To As Boolean, Patch_Ser As Integer, Patch_Name As String, IsExpire As Boolean, Unt_Id As Integer, Unt_Name As String, Unt_GroupId As Integer, Unt_Q As Double, Current_Unt_Id As Integer, Current_Unt_Name As String, Current_Unt_Q As Double, NetQ_Qsetup_CurrentQ As Double, Transaction_Date_Create As DateTime, Transaction_Save As Boolean, Transaction_Submit As Boolean, Transaction_Cancel As Boolean, Del As Boolean, Transaction_Patch As String, Authulized As Boolean, Gard As DateTime) As Integer
        ' Implementation
        Return 0
    End Function

    Public Function GetPatchHead(transactionCode As Object, transactionType As Integer) As String
        ' Implementation
        Return ""
    End Function

    Public ReadOnly Property GetTransCodeSales As String
        Get
            Dim transCode As String = ""
            Try
                ' Get the current date in YYYYMMDD format
                Dim dateStr As String = DateTime.Now.ToString("yyyyMMdd")

                ' Get the last transaction code for sales
                Dim sql As String = "SELECT MAX(Transaction_Code) FROM Transaction_HeadTbl "
                sql += "WHERE Transaction_Code LIKE 'S%' AND Transaction_Code LIKE '%" & dateStr & "'"

                Conn.OpenConnection()
                Dim cmd As New SqlCommand(sql, Conn.SqlConn)
                Dim result = cmd.ExecuteScalar()

                If result IsNot Nothing AndAlso Not DBNull.Value.Equals(result) Then
                    ' Extract the sequence number and increment it
                    Dim lastCode As String = result.ToString()
                    Dim seqStr As String = lastCode.Substring(1, 6)
                    Dim seq As Integer = Integer.Parse(seqStr) + 1

                    ' Format the new transaction code
                    transCode = "S" & seq.ToString("000000") & dateStr
                Else
                    ' If no previous transaction, start with 1
                    transCode = "S000001" & dateStr
                End If

                Conn.CloseConnection()
            Catch ex As Exception
                Console.WriteLine($"Error generating sales transaction code: {ex.Message}")
                transCode = "S" & DateTime.Now.Ticks.ToString().Substring(0, 6) & DateTime.Now.ToString("yyyyMMdd")
            End Try

            Return transCode
        End Get
    End Property

    Public ReadOnly Property GetTransCodeProduction As String
        Get
            Dim transCode As String = ""
            Try
                ' Get the current date in YYYYMMDD format
                Dim dateStr As String = DateTime.Now.ToString("yyyyMMdd")

                ' Get the last transaction code for production
                Dim sql As String = "SELECT MAX(Transaction_Code) FROM Transaction_HeadTbl "
                sql += "WHERE Transaction_Code LIKE 'P%' AND Transaction_Code LIKE '%" & dateStr & "'"

                Conn.OpenConnection()
                Dim cmd As New SqlCommand(sql, Conn.SqlConn)
                Dim result = cmd.ExecuteScalar()

                If result IsNot Nothing AndAlso Not DBNull.Value.Equals(result) Then
                    ' Extract the sequence number and increment it
                    Dim lastCode As String = result.ToString()
                    Dim seqStr As String = lastCode.Substring(1, 6)
                    Dim seq As Integer = Integer.Parse(seqStr) + 1

                    ' Format the new transaction code
                    transCode = "P" & seq.ToString("000000") & dateStr
                Else
                    ' If no previous transaction, start with 1
                    transCode = "P000001" & dateStr
                End If

                Conn.CloseConnection()
            Catch ex As Exception
                Console.WriteLine($"Error generating production transaction code: {ex.Message}")
                transCode = "P" & DateTime.Now.Ticks.ToString().Substring(0, 6) & DateTime.Now.ToString("yyyyMMdd")
            End Try

            Return transCode
        End Get
    End Property

    Public Function CheckValiDate(costCenterId As Integer, transactionDate As DateTime, costCenterName As String) As Boolean
        Dim isValid As Boolean = True

        Try
            ' Check if the date is valid for the cost center
            Dim sql As String = "SELECT COUNT(*) FROM Transaction_HeadTbl "
            sql += $"WHERE CostCenter_Id = {costCenterId} AND Transaction_Date > @TransDate"

            Conn.OpenConnection()
            Dim cmd As New SqlCommand(sql, Conn.SqlConn)
            cmd.Parameters.AddWithValue("@TransDate", transactionDate)
            Dim count = Convert.ToInt32(cmd.ExecuteScalar())

            ' If there are transactions with dates after the provided date, it's invalid
            isValid = (count = 0)

            Conn.CloseConnection()
        Catch ex As Exception
            Console.WriteLine($"Error checking valid date: {ex.Message}")
        End Try

        Return isValid
    End Function

    Public Sub DeleteQuantityNow(Optional productId As Integer = 0, Optional costCenterId As Integer = 0, Optional quantity As Decimal = 0, Optional otherParams As Object = Nothing)
        Try
            If productId > 0 And costCenterId > 0 Then
                ' Get current quantity
                Dim currentQty As Decimal = GetQuantityNow(productId, costCenterId, "", 0)

                ' Calculate new quantity
                Dim newQty As Decimal = Math.Max(0, currentQty - quantity)

                ' Update stock on hand
                ClsOnHand.UpdateStockOnHand(productId, costCenterId, newQty)
            End If
        Catch ex As Exception
            Console.WriteLine($"Error deleting quantity: {ex.Message}")
        End Try
    End Sub

    Public Function ShowProductIdExpireDate_(productId As Integer, costCenterId As Integer, Optional otherParams As Object = Nothing) As DataTable
        Dim dt As New DataTable()

        Try
            Dim sql As String = "SELECT p.Patch_Ser, p.Patch_Name, p.Expire_Date, p.Quantity "
            sql += "FROM PatchTbl p "
            sql += $"WHERE p.Product_Id = {productId} AND p.CostCenter_Id = {costCenterId} "
            sql += "AND p.Quantity > 0 AND p.IsUse = 0 "
            sql += "ORDER BY p.Expire_Date"

            dt = Conn.SELECT_TXT(sql)
        Catch ex As Exception
            Console.WriteLine($"Error getting product expire date: {ex.Message}")
        End Try

        Return dt
    End Function

    Public Function ProcessRecipeRecursive(productId As Integer, quantity As Decimal, costCenterId As Integer, Optional recursionLevel As Integer = 0, Optional parentProductIds As List(Of Integer) = Nothing) As RecipeCalculationResult
        Dim result As New RecipeCalculationResult()
        Dim startTime As DateTime = DateTime.Now

        ' Initialize parent product IDs list if not provided
        If parentProductIds Is Nothing Then
            parentProductIds = New List(Of Integer)()
        End If

        ' Check for circular references
        If parentProductIds.Contains(productId) Then
            result.Success = False
            result.ErrorMessage = $"Circular reference detected for product ID {productId}"
            Return result
        End If

        ' Add current product to parent list for recursive calls
        parentProductIds.Add(productId)

        Try
            ' Get product details
            Dim productDt As DataTable = GetProductDetails(productId)
            If productDt.Rows.Count = 0 Then
                result.Success = False
                result.ErrorMessage = $"Product with ID {productId} not found"
                Return result
            End If

            ' Get recipe details
            Dim recipeDt As DataTable = ShowWithProduct_IdAll(productId)

            ' If no recipe found, treat as a basic ingredient
            If recipeDt.Rows.Count = 0 Then
                ' Check if we have enough stock
                Dim availableCostCenters = GetAvailableCostCenters(productId)
                Dim totalAvailable As Decimal = 0

                For Each cc In availableCostCenters
                    totalAvailable += cc.AvailableQuantity
                Next

                If totalAvailable < quantity Then
                    result.Success = False
                    result.ErrorMessage = $"Insufficient stock for product ID {productId}. Required: {quantity}, Available: {totalAvailable}"
                    Return result
                End If

                ' Distribute the deduction across cost centers based on priority
                Dim deductions = DistributeIngredientDeduction(productId, quantity, availableCostCenters)

                ' Add to ingredient deductions
                Dim ingredient As New IngredientDeduction()
                ingredient.ProductId = productId
                ingredient.ProductCode = productDt.Rows(0)("Product_Code").ToString()
                ingredient.ProductName = productDt.Rows(0)("Product_Name").ToString()
                ingredient.RequiredQuantity = quantity
                ingredient.AvailableQuantity = totalAvailable
                ingredient.DeductedQuantity = quantity
                ingredient.RecipeLevel = recursionLevel
                ingredient.SourceCostCenters = deductions

                ' Calculate cost
                Dim totalCost As Decimal = 0
                For Each deduction In deductions
                    totalCost += deduction.TotalCost
                Next

                ingredient.UnitCost = If(quantity > 0, totalCost / quantity, 0)
                ingredient.TotalCost = totalCost

                result.IngredientDeductions.Add(ingredient)
                result.TotalCost += totalCost
                result.TotalIngredientsProcessed += 1
            Else
                ' Process each ingredient in the recipe
                For Each row As DataRow In recipeDt.Rows
                    Dim ingredientId As Integer = Convert.ToInt32(row("Recipe_Product_Id"))
                    Dim recipeQuantity As Decimal = Convert.ToDecimal(row("Recipe_Quantity"))
                    Dim requiredQuantity As Decimal = recipeQuantity * quantity

                    ' Recursively process this ingredient
                    Dim ingredientResult = ProcessRecipeRecursive(ingredientId, requiredQuantity, costCenterId, recursionLevel + 1, New List(Of Integer)(parentProductIds))

                    ' If any ingredient fails, the whole recipe fails
                    If Not ingredientResult.Success Then
                        result.Success = False
                        result.ErrorMessage = ingredientResult.ErrorMessage
                        Return result
                    End If

                    ' Add ingredient deductions to the result
                    For Each deduction In ingredientResult.IngredientDeductions
                        ' Mark as recursive ingredient
                        deduction.IsRecursiveIngredient = True
                        deduction.ParentProductId = productId
                        result.IngredientDeductions.Add(deduction)
                    Next

                    ' Add to total cost
                    result.TotalCost += ingredientResult.TotalCost
                    result.TotalIngredientsProcessed += ingredientResult.TotalIngredientsProcessed

                    ' Track maximum recursion depth
                    result.RecursiveDepth = Math.Max(result.RecursiveDepth, ingredientResult.RecursiveDepth + 1)
                Next

                ' Add production requirement
                Dim requirement As New ProductionRequirement()
                requirement.ProductId = productId
                requirement.ProductCode = productDt.Rows(0)("Product_Code").ToString()
                requirement.ProductName = productDt.Rows(0)("Product_Name").ToString()
                requirement.RequiredQuantity = quantity
                requirement.EstimatedCost = result.TotalCost
                requirement.RecursionLevel = recursionLevel
                requirement.ParentProductIds = parentProductIds

                result.ProductionRequired.Add(requirement)
            End If

            ' Calculate processing time
            result.ProcessingTime = DateTime.Now - startTime
        Catch ex As Exception
            result.Success = False
            result.ErrorMessage = $"Error processing recipe: {ex.Message}"
        End Try

        Return result
    End Function

    Public Function DistributeIngredientDeduction(productId As Integer, requiredQuantity As Decimal, availableCostCenters As List(Of CostCenterPriority)) As List(Of CostCenterDeduction)
        Dim result As New List(Of CostCenterDeduction)()
        Dim remainingQuantity As Decimal = requiredQuantity

        ' Sort cost centers by priority (lower number = higher priority)
        Dim sortedCostCenters = availableCostCenters.OrderBy(Function(cc) cc.Priority).ToList()

        For Each costCenter In sortedCostCenters
            If remainingQuantity <= 0 Then
                Exit For
            End If

            ' Calculate how much we can take from this cost center
            Dim deductQuantity As Decimal = Math.Min(remainingQuantity, costCenter.AvailableQuantity)

            If deductQuantity > 0 Then
                Dim deduction As New CostCenterDeduction()
                deduction.CostCenterId = costCenter.CostCenterId
                deduction.CostCenterName = costCenter.CostCenterName
                deduction.DeductedQuantity = deductQuantity
                deduction.UnitCost = costCenter.AvgCost
                deduction.TotalCost = deductQuantity * costCenter.AvgCost
                deduction.Priority = costCenter.Priority

                result.Add(deduction)
                remainingQuantity -= deductQuantity
            End If
        Next

        ' If we couldn't fulfill the entire quantity, return empty list
        If remainingQuantity > 0 Then
            Return New List(Of CostCenterDeduction)()
        End If

        Return result
    End Function

    Public Function ProcessProductionOrder(productId As Integer, quantity As Decimal, costCenterId As Integer) As Boolean
        Dim result As Boolean = False

        Try
            ' Calculate recipe requirements
            Dim recipeResult = ProcessRecipeRecursive(productId, quantity, costCenterId)
            
            ' Check if recipe processing was successful
            If Not recipeResult.Success Then
                Console.WriteLine($"Failed to process production order: {recipeResult.ErrorMessage}")
                Return False
            End If

            ' Get product details
            Dim productDt As DataTable = GetProductDetails(productId)
            If productDt.Rows.Count = 0 Then
                Console.WriteLine($"Product with ID {productId} not found")
                Return False
            End If

            Dim productRow As DataRow = productDt.Rows(0)
            Dim productCode As String = productRow("Product_Code").ToString()
            Dim productName As String = productRow("Product_Name").ToString()

            ' Get cost center details
            Dim costCenterDt As DataTable = GetCostCenterDataTrue(costCenterId)
            If costCenterDt.Rows.Count = 0 Then
                Console.WriteLine($"Cost center with ID {costCenterId} not found")
                Return False
            End If

            Dim costCenterName As String = costCenterDt.Rows(0)("CostCenter_Name").ToString()

            ' Generate transaction code
            Dim transCode As String = GetTransCodeProduction

            ' Create transaction header
            Head_Insert(transCode, costCenterId, 0, "", "Production Order", "",
                       Convert.ToDouble(recipeResult.TotalCost), 0, Convert.ToDouble(recipeResult.TotalCost),
                       True, True, 1, 0, 0, Convert.ToDouble(recipeResult.TotalCost),
                       False, DateTime.Now, DateTime.Now, "", 0, "", 5)  ' 5 is for production transaction

            ' Create transaction details for the produced item
            Details_Save(transCode, productId, productCode, productName, 0, Convert.ToDouble(quantity), 0, 0,
                       Convert.ToDouble(recipeResult.TotalCost / quantity), Convert.ToDouble(recipeResult.TotalCost),
                       0, "", costCenterId, costCenterName, 0, "", 0, "",
                       False, False, 0, "", False, 0, "", 0, 0, 0, "", 0, 0,
                       DateTime.Now, True, True, False, False, "", False, DateTime.Now,
                       True, Convert.ToDouble(recipeResult.TotalCost / quantity))

            ' Process each ingredient deduction
            For Each ingredient In recipeResult.IngredientDeductions
                ' Skip recursive ingredients as they're already processed
                If ingredient.IsRecursiveIngredient Then
                    Continue For
                End If

                ' Process each cost center deduction
                For Each deduction In ingredient.SourceCostCenters
                    ' Deduct from stock
                    DeleteQuantityNow(ingredient.ProductId, deduction.CostCenterId, deduction.DeductedQuantity)

                    ' Create transaction detail for the ingredient
                    Details_Save(transCode, ingredient.ProductId, ingredient.ProductCode, ingredient.ProductName,
                               0, Convert.ToDouble(-deduction.DeductedQuantity), 0, 0,
                               Convert.ToDouble(deduction.UnitCost), Convert.ToDouble(-deduction.TotalCost),
                               deduction.CostCenterId, deduction.CostCenterName, 0, "", 0, "", 0, "",
                               False, False, 0, "", False, 0, "", 0, 0, 0, "", 0, 0,
                               DateTime.Now, True, True, False, False, "", False, DateTime.Now,
                               True, Convert.ToDouble(deduction.UnitCost), Convert.ToDouble(ingredient.RequiredQuantity))
                Next
            Next

            ' Update stock for the produced item
            Dim currentStock As Decimal = GetQuantityNow(productId, costCenterId, productCode, Convert.ToDouble(quantity))
            ClsOnHand.UpdateStockOnHand(productId, costCenterId, currentStock + quantity)

            result = True
        Catch ex As Exception
            Console.WriteLine($"Error processing production order: {ex.Message}")
        End Try

        Return result
    End Function

    Public Function ShowDataParametars_() As DataTable
        Dim dt As New DataTable()

        Try
            Dim sql As String = "SELECT * FROM ParametersTbl"
            dt = Conn.SELECT_TXT(sql)
        Catch ex As Exception
            Console.WriteLine($"Error getting parameters: {ex.Message}")
        End Try

        Return dt
    End Function

    Public Function GetQuantityNow(productId As Integer, costCenterId As Integer, productCode As String, netQuantity As Double, Optional otherParams As Object = Nothing) As Decimal
        ' Implementation
        Return 0
    End Function

    Public Function ShowUsdFalse_() As DataTable
        ' Implementation
        Return New DataTable()
    End Function

    Public Sub UpdatePatchUse_True(patchSer As Integer)
        Try
            Dim sql As String = $"UPDATE PatchTbl SET IsUse = 1 WHERE Patch_Ser = {patchSer}"

            Conn.OpenConnection()
            Dim cmd As New SqlCommand(sql, Conn.SqlConn)
            cmd.ExecuteNonQuery()
            Conn.CloseConnection()
        Catch ex As Exception
            Console.WriteLine($"Error updating patch use: {ex.Message}")
        End Try
    End Sub

    Public Function ShowSerOpen_() As DataTable
        ' Implementation
        Return New DataTable()
    End Function

    Public Function CheckSumQPatch(productId As Integer, quantity As Decimal) As Boolean
        ' Implementation
        Return True
    End Function

    Public Sub UpdateUsePatch(patchSer As Integer, productId As Integer, costCenterId As Integer, Optional otherParams As Object = Nothing)
        ' Implementation
    End Sub

    Public Function GetExpiredData(productId As Integer, costCenterId As Integer, Optional otherParams As Object = Nothing) As DataTable
        ' Implementation
        Return New DataTable()
    End Function

    Public Function GetCostCenterDataTrue(costCenterId As Integer) As DataTable
        Dim dt As New DataTable()

        Try
            Dim sql As String = "SELECT * FROM CostCenterTbl WHERE CostCenter_Id = @CostCenterId AND IsUse = 1"

            Conn.OpenConnection()
            Dim cmd As New SqlCommand(sql, Conn.SqlConn)
            cmd.Parameters.AddWithValue("@CostCenterId", costCenterId)

            Dim adapter As New SqlDataAdapter(cmd)
            adapter.Fill(dt)

            Conn.CloseConnection()
        Catch ex As Exception
            Console.WriteLine($"Error getting cost center data: {ex.Message}")
        End Try

        Return dt
    End Function

    Public Function GetSOHDataGetting(productId As Integer, costCenterId As String) As DataTable
        Dim dt As New DataTable()

        Try
            Dim sql As String = "SELECT soh.*, cc.CostCenter_Name "
            sql += "FROM StockOnHandTbl soh "
            sql += "INNER JOIN CostCenterTbl cc ON soh.CostCenter_Id = cc.CostCenter_Id "
            sql += "WHERE soh.Product_Id = @ProductId "

            If Not String.IsNullOrEmpty(costCenterId) Then
                sql += "AND soh.CostCenter_Id = @CostCenterId "
            End If

            Conn.OpenConnection()
            Dim cmd As New SqlCommand(sql, Conn.SqlConn)
            cmd.Parameters.AddWithValue("@ProductId", productId)

            If Not String.IsNullOrEmpty(costCenterId) Then
                cmd.Parameters.AddWithValue("@CostCenterId", costCenterId)
            End If

            Dim adapter As New SqlDataAdapter(cmd)
            adapter.Fill(dt)

            Conn.CloseConnection()
        Catch ex As Exception
            Console.WriteLine($"Error getting stock on hand data: {ex.Message}")
        End Try

        Return dt
    End Function

    Public Function GetCostCenterData(settingId As Integer) As DataTable
        Dim dt As New DataTable()

        Try
            Dim sql As String = "SELECT cc.* "
            sql += "FROM CostCenterTbl cc "
            sql += "INNER JOIN POSSetting ps ON cc.CostCenter_Id = ps.CostCenter_IdPOS "
            sql += "WHERE ps.Setting_Id = @SettingId AND cc.IsUse = 1"

            Conn.OpenConnection()
            Dim cmd As New SqlCommand(sql, Conn.SqlConn)
            cmd.Parameters.AddWithValue("@SettingId", settingId)

            Dim adapter As New SqlDataAdapter(cmd)
            adapter.Fill(dt)

            Conn.CloseConnection()
        Catch ex As Exception
            Console.WriteLine($"Error getting cost center data: {ex.Message}")
        End Try

        Return dt
    End Function

    Public Function GetProductData(productCode As String) As DataTable
        Dim dt As New DataTable()

        Try
            Dim sql As String = "SELECT * FROM ProductsTbl "
            sql += $"WHERE Product_Code = '{productCode}'"

            dt = Conn.SELECT_TXT(sql)
        Catch ex As Exception
            Console.WriteLine($"Error getting product data: {ex.Message}")
        End Try

        Return dt
    End Function

    Public Function GetSettingPOS(companyId As Integer, brandId As Integer, costCenterId As Integer) As DataTable
        Dim dt As New DataTable()

        Try
            Dim sql As String = "SELECT * FROM POSSetting "
            sql += $"WHERE Company_IdPOS = {companyId} AND Brand_IdPOS = {brandId} AND CostCenter_IdPOS = {costCenterId}"

            dt = Conn.SELECT_TXT(sql)
        Catch ex As Exception
            Console.WriteLine($"Error getting POS settings: {ex.Message}")
        End Try

        Return dt
    End Function

    Public Function SalePosLoadInDGVSCM() As DataTable
        Dim dt As New DataTable()

        Try
            ' Create a table structure for the grid
            dt.Columns.Add("CostCenterPOS_Id", GetType(Integer))
            dt.Columns.Add("CostCenterPOS_Name", GetType(String))
            dt.Columns.Add("CostCenter_Id_To", GetType(Integer))
            dt.Columns.Add("CostCenter_Name_To", GetType(String))
            dt.Columns.Add("Transaction_Code", GetType(Integer))
            dt.Columns.Add("Product_Id", GetType(Integer))
            dt.Columns.Add("Product_Code", GetType(String))
            dt.Columns.Add("Product_Name", GetType(String))
            dt.Columns.Add("Reciving_Q", GetType(Double))
            dt.Columns.Add("Cost_Product", GetType(Double))
            dt.Columns.Add("CostTotalLine", GetType(Double))
            dt.Columns.Add("Patch_Ser", GetType(Integer))
            dt.Columns.Add("Patch_Name", GetType(String))
            dt.Columns.Add("IsExpire", GetType(Boolean))
            dt.Columns.Add("Unt_Id", GetType(Integer))
            dt.Columns.Add("Unt_Name", GetType(String))
            dt.Columns.Add("Unt_GroupId", GetType(Integer))
            dt.Columns.Add("Unt_Q", GetType(Double))
            dt.Columns.Add("Current_Unt_Id", GetType(Integer))
            dt.Columns.Add("Current_Unt_Name", GetType(String))
            dt.Columns.Add("Current_Unt_Q", GetType(Double))
            dt.Columns.Add("NetQ_Qsetup_CurrentQ", GetType(Double))
            dt.Columns.Add("Transaction_Date_Create", GetType(DateTime))
            dt.Columns.Add("CompanyPOS_Id", GetType(Integer))
            dt.Columns.Add("CompanyPOS_Name", GetType(String))
            dt.Columns.Add("OutLetPOS_Id", GetType(Integer))
            dt.Columns.Add("OutLetPOS_Name", GetType(String))
            dt.Columns.Add("ChDID", GetType(Integer))
        Catch ex As Exception
            Console.WriteLine($"Error loading sales POS data: {ex.Message}")
        End Try

        Return dt
    End Function

    Public Function GetLasttrans_date(fromDate As DateTime) As DateTime
        Dim lastDate As DateTime = fromDate

        Try
            Dim sql As String = "SELECT MAX(Transaction_Date_Create) FROM Transaction_DetailsTbl "
            sql += "WHERE Transaction_Id = 9"  ' Sales transactions

            Conn.OpenConnection()
            Dim cmd As New SqlCommand(sql, Conn.SqlConn)
            Dim result = cmd.ExecuteScalar()

            If result IsNot Nothing AndAlso Not DBNull.Value.Equals(result) Then
                lastDate = Convert.ToDateTime(result)
            End If

            Conn.CloseConnection()
        Catch ex As Exception
            Console.WriteLine($"Error getting last transaction date: {ex.Message}")
        End Try

        Return lastDate
    End Function

    ' Other existing methods
    Public Function LoadPosData(Fromdate As DateTime, Todate As DateTime, CostCenter_IdPOS As Integer, Optional IsLoding As Boolean = False) As DataTable
        Dim dt As New DataTable()

        Try
            Dim sql As String = "SELECT * FROM v_ProcIFC "
            sql += $"WHERE center = {CostCenter_IdPOS} AND IsIFCDone = 0 "
            sql += $"AND statistdate BETWEEN '{Fromdate.ToString("yyyy-MM-dd 00:00:00")}' AND '{Todate.ToString("yyyy-MM-dd 23:59:59")}' "

            If IsLoding Then
                sql += "AND plu = 0 "
            End If

            sql += "ORDER BY mandant, outlet, center, plu"

            dt = Conn.SELECT_TXTPOS(sql)
        Catch ex As Exception
            Console.WriteLine($"Error loading POS data: {ex.Message}")
        End Try

        Return dt
    End Function

    Public Sub CheckExistV_ProcIFC()
        Try
            ' Check if the view exists
            Dim sql As String = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_NAME = 'v_ProcIFC'"

            Conn.OpenConnection()
            Dim cmd As New SqlCommand(sql, Conn.SqlConn)
            Dim count = Convert.ToInt32(cmd.ExecuteScalar())

            ' If the view doesn't exist, create it
            If count = 0 Then
                sql = "CREATE VIEW v_ProcIFC AS "
                sql += "SELECT t.mandant, t.outlet, t.center, t.plu, t.statistdate, t.quantity, t.amount, "
                sql += "0 AS IsIFCDone "
                sql += "FROM TransactionsTbl t "
                sql += "WHERE t.IsProcessed = 0"

                cmd = New SqlCommand(sql, Conn.SqlConn)
                cmd.ExecuteNonQuery()
            End If

            Conn.CloseConnection()
        Catch ex As Exception
            Console.WriteLine($"Error checking v_ProcIFC: {ex.Message}")
        End Try
    End Sub

    Public Function GetSOHData(productId As Integer, costCenterId As Integer) As DataTable
        Dim dt As New DataTable()

        Try
            Dim sql As String = "SELECT soh.*, cc.CostCenter_Name "
            sql += "FROM StockOnHandTbl_POS soh "
            sql += "INNER JOIN CostCenterLinkPOS cc ON soh.CostCenter_Id = cc.CostCenter_Id "
            sql += $"WHERE soh.Product_Id = {productId} AND soh.CostCenter_Id = {costCenterId}"

            dt = Conn.SELECT_TXT(sql)
        Catch ex As Exception
            Console.WriteLine($"Error getting SOH data: {ex.Message}")
        End Try

        Return dt
    End Function

    Public Sub Head_Insert(Transaction_Code As String, CostCenter_Id As Integer, Suppliers_Id As Integer, CostCenter_Supplier As String, Remarks As String, Invoice_No As String, Amount_Bill As Double, Tax_Bill As Double, Total_Amount As Double, Transaction_Save As Boolean, Transaction_Submit As Boolean, User_Id As Integer, Utholization_Id As Integer, Transaction_PaidAmount As Double, Transaction_NetAmount As Double, Authulized As Boolean, Transaction_Date As DateTime, Transaction_Date_Submit As DateTime, Transaction_Patch As String, User_Id_Submit As Integer, Invoice_NoReciving As String, TransId As Integer)
        Try
            Dim sql As String = "INSERT INTO Transaction_HeadTbl (Transaction_Code, CostCenter_Id, Suppliers_Id, CostCenter_Supplier, "
            sql += "Remarks, Invoice_No, Amount_Bill, Tax_Bill, Total_Amount, Transaction_Save, Transaction_Submit, "
            sql += "User_Id, Utholization_Id, Transaction_PaidAmount, Transaction_NetAmount, Authulized, "
            sql += "Transaction_Date, Transaction_Date_Submit, Transaction_Patch, User_Id_Submit, Invoice_NoReciving, Transaction_Id) "
            sql += "VALUES (@Transaction_Code, @CostCenter_Id, @Suppliers_Id, @CostCenter_Supplier, "
            sql += "@Remarks, @Invoice_No, @Amount_Bill, @Tax_Bill, @Total_Amount, @Transaction_Save, @Transaction_Submit, "
            sql += "@User_Id, @Utholization_Id, @Transaction_PaidAmount, @Transaction_NetAmount, @Authulized, "
            sql += "@Transaction_Date, @Transaction_Date_Submit, @Transaction_Patch, @User_Id_Submit, @Invoice_NoReciving, @TransId)"

            Conn.OpenConnection()
            Dim cmd As New SqlCommand(sql, Conn.SqlConn)

            cmd.Parameters.AddWithValue("@Transaction_Code", Transaction_Code)
            cmd.Parameters.AddWithValue("@CostCenter_Id", CostCenter_Id)
            cmd.Parameters.AddWithValue("@Suppliers_Id", Suppliers_Id)
            cmd.Parameters.AddWithValue("@CostCenter_Supplier", CostCenter_Supplier)
            cmd.Parameters.AddWithValue("@Remarks", Remarks)
            cmd.Parameters.AddWithValue("@Invoice_No", Invoice_No)
            cmd.Parameters.AddWithValue("@Amount_Bill", Amount_Bill)
            cmd.Parameters.AddWithValue("@Tax_Bill", Tax_Bill)
            cmd.Parameters.AddWithValue("@Total_Amount", Total_Amount)
            cmd.Parameters.AddWithValue("@Transaction_Save", Transaction_Save)
            cmd.Parameters.AddWithValue("@Transaction_Submit", Transaction_Submit)
            cmd.Parameters.AddWithValue("@User_Id", User_Id)
            cmd.Parameters.AddWithValue("@Utholization_Id", Utholization_Id)
            cmd.Parameters.AddWithValue("@Transaction_PaidAmount", Transaction_PaidAmount)
            cmd.Parameters.AddWithValue("@Transaction_NetAmount", Transaction_NetAmount)
            cmd.Parameters.AddWithValue("@Authulized", Authulized)
            cmd.Parameters.AddWithValue("@Transaction_Date", Transaction_Date)
            cmd.Parameters.AddWithValue("@Transaction_Date_Submit", Transaction_Date_Submit)

            If String.IsNullOrEmpty(Transaction_Patch) Then
                cmd.Parameters.AddWithValue("@Transaction_Patch", DBNull.Value)
            Else
                cmd.Parameters.AddWithValue("@Transaction_Patch", Transaction_Patch)
            End If

            cmd.Parameters.AddWithValue("@User_Id_Submit", User_Id_Submit)
            cmd.Parameters.AddWithValue("@Invoice_NoReciving", Invoice_NoReciving)
            cmd.Parameters.AddWithValue("@TransId", TransId)

            cmd.ExecuteNonQuery()
            Conn.CloseConnection()
        Catch ex As Exception
            Console.WriteLine($"Error inserting transaction head: {ex.Message}")
        End Try
    End Sub

    Public Sub Details_Save(Transaction_Code As String, Product_Id As Integer, Product_Code As String, Product_Name As String, Order_Q As Double, Reciving_Q As Double, Invoice_Q As Double, Return_Q As Double, Cost_Product As Double, CostTotalLine As Double, CostCenter_Id_Frm As Integer, CostCenter_Name_Frm As String, CostCenter_Id_To As Integer, CostCenter_Name_To As String, Suppliers_Id_Frm As Integer, Suppliers_Name_Frm As String, Suppliers_Id_To As Integer, Suppliers_Name_To As String, Supplier_Frm As Boolean, Supplier_To As Boolean, Patch_Ser As Integer, Patch_Name As String, IsExpire As Boolean, Unt_Id As Integer, Unt_Name As String, Unt_GroupId As Integer, Unt_Q As Double, Current_Unt_Id As Integer, Current_Unt_Name As String, Current_Unt_Q As Double, NetQ_Qsetup_CurrentQ As Double, Transaction_Date_Create As DateTime, Transaction_Save As Boolean, Transaction_Submit As Boolean, Transaction_Cancel As Boolean, Del As Boolean, Transaction_Patch As String, Authulized As Boolean, Gard As DateTime, Optional IsProductions As Boolean = False, Optional AvCost As Double = 0, Optional Required_Quantity As Double = 0, Optional UsedQuantity As Double = 0, Optional Rid As Integer = 0)
        Try
            Dim sql As String = "INSERT INTO Transaction_DetailsTbl (Transaction_Code, Product_Id, Product_Code, Product_Name, "
            sql += "Order_Q, Reciving_Q, Invoice_Q, Return_Q, Cost_Product, CostTotalLine, "
            sql += "CostCenter_Id_Frm, CostCenter_Name_Frm, CostCenter_Id_To, CostCenter_Name_To, "
            sql += "Suppliers_Id_Frm, Suppliers_Name_Frm, Suppliers_Id_To, Suppliers_Name_To, "
            sql += "Supplier_Frm, Supplier_To, Patch_Ser, Patch_Name, IsExpire, "
            sql += "Unt_Id, Unt_Name, Unt_GroupId, Unt_Q, Current_Unt_Id, Current_Unt_Name, Current_Unt_Q, "
            sql += "NetQ_Qsetup_CurrentQ, Transaction_Date_Create, Transaction_Save, Transaction_Submit, "
            sql += "Transaction_Cancel, Del, Transaction_Patch, Authulized, GardDate, Transaction_Id, "
            sql += "IsRecipe, AvCost) "
            sql += "VALUES (@Transaction_Code, @Product_Id, @Product_Code, @Product_Name, "
            sql += "@Order_Q, @Reciving_Q, @Invoice_Q, @Return_Q, @Cost_Product, @CostTotalLine, "
            sql += "@CostCenter_Id_Frm, @CostCenter_Name_Frm, @CostCenter_Id_To, @CostCenter_Name_To, "
            sql += "@Suppliers_Id_Frm, @Suppliers_Name_Frm, @Suppliers_Id_To, @Suppliers_Name_To, "
            sql += "@Supplier_Frm, @Supplier_To, @Patch_Ser, @Patch_Name, @IsExpire, "
            sql += "@Unt_Id, @Unt_Name, @Unt_GroupId, @Unt_Q, @Current_Unt_Id, @Current_Unt_Name, @Current_Unt_Q, "
            sql += "@NetQ_Qsetup_CurrentQ, @Transaction_Date_Create, @Transaction_Save, @Transaction_Submit, "
            sql += "@Transaction_Cancel, @Del, @Transaction_Patch, @Authulized, @Gard, "
            sql += "(SELECT Transaction_Id FROM Transaction_HeadTbl WHERE Transaction_Code = @Transaction_Code), "
            sql += "@IsProductions, @AvCost)"

            Conn.OpenConnection()
            Dim cmd As New SqlCommand(sql, Conn.SqlConn)

            cmd.Parameters.AddWithValue("@Transaction_Code", Transaction_Code)
            cmd.Parameters.AddWithValue("@Product_Id", Product_Id)
            cmd.Parameters.AddWithValue("@Product_Code", Product_Code)
            cmd.Parameters.AddWithValue("@Product_Name", Product_Name)
            cmd.Parameters.AddWithValue("@Order_Q", Order_Q)
            cmd.Parameters.AddWithValue("@Reciving_Q", Reciving_Q)
            cmd.Parameters.AddWithValue("@Invoice_Q", Invoice_Q)
            cmd.Parameters.AddWithValue("@Return_Q", Return_Q)
            cmd.Parameters.AddWithValue("@Cost_Product", Cost_Product)
            cmd.Parameters.AddWithValue("@CostTotalLine", CostTotalLine)
            cmd.Parameters.AddWithValue("@CostCenter_Id_Frm", CostCenter_Id_Frm)
            cmd.Parameters.AddWithValue("@CostCenter_Name_Frm", CostCenter_Name_Frm)
            cmd.Parameters.AddWithValue("@CostCenter_Id_To", CostCenter_Id_To)
            cmd.Parameters.AddWithValue("@CostCenter_Name_To", CostCenter_Name_To)
            cmd.Parameters.AddWithValue("@Suppliers_Id_Frm", Suppliers_Id_Frm)
            cmd.Parameters.AddWithValue("@Suppliers_Name_Frm", Suppliers_Name_Frm)
            cmd.Parameters.AddWithValue("@Suppliers_Id_To", Suppliers_Id_To)
            cmd.Parameters.AddWithValue("@Suppliers_Name_To", Suppliers_Name_To)
            cmd.Parameters.AddWithValue("@Supplier_Frm", Supplier_Frm)
            cmd.Parameters.AddWithValue("@Supplier_To", Supplier_To)
            cmd.Parameters.AddWithValue("@Patch_Ser", Patch_Ser)
            cmd.Parameters.AddWithValue("@Patch_Name", Patch_Name)
            cmd.Parameters.AddWithValue("@IsExpire", IsExpire)
            cmd.Parameters.AddWithValue("@Unt_Id", Unt_Id)
            cmd.Parameters.AddWithValue("@Unt_Name", Unt_Name)
            cmd.Parameters.AddWithValue("@Unt_GroupId", Unt_GroupId)
            cmd.Parameters.AddWithValue("@Unt_Q", Unt_Q)
            cmd.Parameters.AddWithValue("@Current_Unt_Id", Current_Unt_Id)
            cmd.Parameters.AddWithValue("@Current_Unt_Name", Current_Unt_Name)
            cmd.Parameters.AddWithValue("@Current_Unt_Q", Current_Unt_Q)
            cmd.Parameters.AddWithValue("@NetQ_Qsetup_CurrentQ", NetQ_Qsetup_CurrentQ)
            cmd.Parameters.AddWithValue("@Transaction_Date_Create", Transaction_Date_Create)
            cmd.Parameters.AddWithValue("@Transaction_Save", Transaction_Save)
            cmd.Parameters.AddWithValue("@Transaction_Submit", Transaction_Submit)
            cmd.Parameters.AddWithValue("@Transaction_Cancel", Transaction_Cancel)
            cmd.Parameters.AddWithValue("@Del", Del)
            cmd.Parameters.AddWithValue("@Transaction_Patch", Transaction_Patch)
            cmd.Parameters.AddWithValue("@Authulized", Authulized)
            cmd.Parameters.AddWithValue("@Gard", Gard)
            cmd.Parameters.AddWithValue("@IsProductions", IsProductions)
            cmd.Parameters.AddWithValue("@AvCost", AvCost)

            cmd.ExecuteNonQuery()

            ' Note: Recipe data is retrieved before transaction processing using ShowWithProduct_IdAll
            ' This allows the system to check ingredient availability before creating transactions

            Conn.CloseConnection()
        Catch ex As Exception
            Console.WriteLine($"Error saving details: {ex.Message}")
        End Try
    End Sub

    Public Function ShowWithProduct_IdAll(productId As Integer) As DataTable
        Dim dt As New DataTable()

        Try
            Dim sql As String = "SELECT * FROM Recipe_ProductsTbl "
            sql += $"WHERE Product_Id = {productId} AND Del = 0"

            dt = Conn.SELECT_TXT(sql)
        Catch ex As Exception
            Console.WriteLine($"Error getting product recipe: {ex.Message}")
        End Try

        Return dt
    End Function

    Public Sub UpdatePatcheUseFalse(PatchSer As String)
        Try
            Dim sql As String = $"UPDATE PatchTbl SET IsUse = 0 WHERE Patch_Ser = {PatchSer}"

            Conn.OpenConnection()
            Dim cmd As New SqlCommand(sql, Conn.SqlConn)
            cmd.ExecuteNonQuery()
            Conn.CloseConnection()
        Catch ex As Exception
            Console.WriteLine($"Error updating patch use: {ex.Message}")
        End Try
    End Sub
End Class
