Imports System.Data
Imports System.Windows.Forms

Public Class RecipeProduction_Frm
    Private RecipeProcessor As New RecipeProcessor
    Private IFC_Cls As New IFC_Transaction
    Private CostCenterMgr As New CostCenterManager
    Private Conn As New Conn_Cls
    
    Public Sub New()
        InitializeComponent()
        LoadProductionProducts()
        LoadCostCenters()
    End Sub
    
    Private Sub LoadProductionProducts()
        Try
            ' Get all products that have recipes
            Dim sql As String = "SELECT p.Product_Id, p.Product_Code, p.Product_Name " & _
                              "FROM ProductsTbl p " & _
                              "WHERE EXISTS (SELECT 1 FROM Recipe_ProductsTbl r WHERE r.Product_Id = p.Product_Id) " & _
                              "ORDER BY p.Product_Name"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            ' Bind to combo box
            cmbProduct.DataSource = dt
            cmbProduct.DisplayMember = "Product_Name"
            cmbProduct.ValueMember = "Product_Id"
        Catch ex As Exception
            MessageBox.Show($"Error loading production products: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub LoadCostCenters()
        Try
            ' Get all cost centers
            Dim dt As DataTable = CostCenterMgr.GetAllCostCenterPriorities()
            
            ' Bind to combo box
            cmbCostCenter.DataSource = dt
            cmbCostCenter.DisplayMember = "CostCenter_Name"
            cmbCostCenter.ValueMember = "CostCenter_Id"
            
            ' Select default cost center if available
            Dim defaultCostCenterId As Integer = CostCenterMgr.GetDefaultCostCenter()
            If defaultCostCenterId > 0 Then
                For i As Integer = 0 To cmbCostCenter.Items.Count - 1
                    Dim row As DataRowView = DirectCast(cmbCostCenter.Items(i), DataRowView)
                    If Convert.ToInt32(row("CostCenter_Id")) = defaultCostCenterId Then
                        cmbCostCenter.SelectedIndex = i
                        Exit For
                    End If
                Next
            End If
        Catch ex As Exception
            MessageBox.Show($"Error loading cost centers: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub btnLoadRecipe_Click(sender As Object, e As EventArgs) Handles btnLoadRecipe.Click
        Try
            If cmbProduct.SelectedIndex = -1 Then
                MessageBox.Show("Please select a product", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If
            
            Dim productId As Integer = Convert.ToInt32(cmbProduct.SelectedValue)
            
            ' Load recipe ingredients
            LoadRecipeIngredients(productId)
            
            ' Enable production button
            btnProduce.Enabled = True
        Catch ex As Exception
            MessageBox.Show($"Error loading recipe: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub LoadRecipeIngredients(productId As Integer)
        Try
            ' Get recipe ingredients
            Dim sql As String = "SELECT r.Recipe_Product_Id, r.Recipe_Product_Code, r.Recipe_Product_Name, " & _
                              "r.Recipe_Quantity, r.Recipe_Price, r.Current_Unt_Name, " & _
                              "CASE WHEN EXISTS (SELECT 1 FROM Recipe_ProductsTbl r2 WHERE r2.Product_Id = r.Recipe_Product_Id) " & _
                              "THEN 'Yes' ELSE 'No' END AS IsProduction " & _
                              "FROM Recipe_ProductsTbl r " & _
                              "WHERE r.Product_Id = " & productId & " " & _
                              "ORDER BY r.Recipe_Product_Name"
            
            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            
            ' Bind to grid
            dgvIngredients.DataSource = dt
            
            ' Format grid
            FormatIngredientsGrid()
            
            ' Update label
            lblRecipeTitle.Text = $"Recipe for {cmbProduct.Text}"
        Catch ex As Exception
            MessageBox.Show($"Error loading recipe ingredients: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub FormatIngredientsGrid()
        ' Set column headers and formatting
        dgvIngredients.Columns("Recipe_Product_Id").HeaderText = "ID"
        dgvIngredients.Columns("Recipe_Product_Code").HeaderText = "Code"
        dgvIngredients.Columns("Recipe_Product_Name").HeaderText = "Ingredient Name"
        dgvIngredients.Columns("Recipe_Quantity").HeaderText = "Quantity"
        dgvIngredients.Columns("Recipe_Price").HeaderText = "Cost"
        dgvIngredients.Columns("Current_Unt_Name").HeaderText = "Unit"
        dgvIngredients.Columns("IsProduction").HeaderText = "Is Production"
        
        ' Set column widths
        dgvIngredients.Columns("Recipe_Product_Id").Width = 50
        dgvIngredients.Columns("Recipe_Product_Code").Width = 80
        dgvIngredients.Columns("Recipe_Product_Name").Width = 200
        dgvIngredients.Columns("Recipe_Quantity").Width = 80
        dgvIngredients.Columns("Recipe_Price").Width = 80
        dgvIngredients.Columns("Current_Unt_Name").Width = 80
        dgvIngredients.Columns("IsProduction").Width = 80
    End Sub
    
    Private Sub btnProduce_Click(sender As Object, e As EventArgs) Handles btnProduce.Click
        Try
            If cmbProduct.SelectedIndex = -1 Then
                MessageBox.Show("Please select a product", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If
            
            If cmbCostCenter.SelectedIndex = -1 Then
                MessageBox.Show("Please select a target cost center", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If
            
            If nudQuantity.Value <= 0 Then
                MessageBox.Show("Please enter a valid quantity", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If
            
            Dim productId As Integer = Convert.ToInt32(cmbProduct.SelectedValue)
            Dim costCenterId As Integer = Convert.ToInt32(cmbCostCenter.SelectedValue)
            Dim quantity As Decimal = Convert.ToDecimal(nudQuantity.Value)
            
            ' Show progress form
            Dim progressForm As New Frm_Progress()
            progressForm.LblStatus.Text = "Processing recipe production..."
            progressForm.Show()
            progressForm.Refresh()
            
            ' Process recipe production
            Dim success As Boolean = RecipeProcessor.ProcessRecipe(productId, quantity, costCenterId)
            
            ' Close progress form
            progressForm.Close()
            
            If success Then
                MessageBox.Show($"Production of {quantity} {cmbProduct.Text} completed successfully", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
                
                ' Refresh recipe
                LoadRecipeIngredients(productId)
            Else
                MessageBox.Show("Production failed. Please check the logs for details.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If
        Catch ex As Exception
            MessageBox.Show($"Error processing production: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        Me.Close()
    End Sub
    
    Private Sub cmbProduct_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbProduct.SelectedIndexChanged
        ' Clear recipe when product changes
        dgvIngredients.DataSource = Nothing
        lblRecipeTitle.Text = "Recipe Details"
        btnProduce.Enabled = False
    End Sub
End Class
